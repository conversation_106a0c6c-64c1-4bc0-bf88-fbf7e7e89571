const RecursiveZipProcessor = require('./unzip');
const path = require('path');

/**
 * Test script for handling large ZIP files (>2GB)
 * This script demonstrates proper configuration for large file processing
 */

async function testLargeFile() {
  console.log('=== Large File ZIP Processing Test ===\n');
  
  // Get command line arguments
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.log('Usage: node large-file-test.js <large-zip-file> [options]');
    console.log('');
    console.log('Options:');
    console.log('  --extract     Extract files (default: list only)');
    console.log('  --output DIR  Output directory (default: ./large-extracted)');
    console.log('  --max-depth N Maximum recursion depth (default: 5)');
    console.log('');
    console.log('Example:');
    console.log('  node large-file-test.js Manuals.zip --extract --output ./manuals');
    console.log('');
    console.log('Memory recommendations for large files:');
    console.log('  node --max-old-space-size=8192 large-file-test.js large-file.zip');
    process.exit(1);
  }

  const zipFile = args[0];
  const shouldExtract = args.includes('--extract');
  
  // Parse output directory
  const outputIndex = args.findIndex(arg => arg === '--output');
  const outputDir = outputIndex !== -1 && args[outputIndex + 1] 
    ? args[outputIndex + 1] 
    : './large-extracted';

  // Parse max depth
  const depthIndex = args.findIndex(arg => arg === '--max-depth');
  const maxDepth = depthIndex !== -1 && args[depthIndex + 1] 
    ? parseInt(args[depthIndex + 1]) 
    : 5;

  // Configure processor for large files
  const processor = new RecursiveZipProcessor({
    maxDepth: maxDepth,
    maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB limit
    outputDir: outputDir,
    preserveStructure: true
  });

  console.log('Configuration:');
  console.log(`- File: ${zipFile}`);
  console.log(`- Max depth: ${maxDepth}`);
  console.log(`- Max file size: ${processor.formatFileSize(processor.maxFileSize)}`);
  console.log(`- Extract: ${shouldExtract ? 'Yes' : 'No (list only)'}`);
  console.log(`- Output directory: ${outputDir}`);
  console.log('');

  // Check Node.js memory configuration
  const v8 = require('v8');
  const heapStats = v8.getHeapStatistics();
  console.log('Node.js Memory Configuration:');
  console.log(`- Heap size limit: ${processor.formatFileSize(heapStats.heap_size_limit)}`);
  console.log(`- Total available size: ${processor.formatFileSize(heapStats.total_available_size)}`);
  console.log('');

  if (heapStats.heap_size_limit < 4 * 1024 * 1024 * 1024) {
    console.log('⚠️  WARNING: Node.js heap size may be too small for very large files.');
    console.log('   Consider running with: node --max-old-space-size=8192 large-file-test.js');
    console.log('');
  }

  try {
    const startTime = Date.now();
    
    if (shouldExtract) {
      console.log('Starting extraction process...\n');
      const result = await processor.processZipFile(zipFile, {
        extract: true,
        outputDir: outputDir
      });
      
      console.log('\n=== EXTRACTION COMPLETED ===');
      console.log(`Files extracted to: ${outputDir}`);
      console.log('Summary:', result.summary);
      
    } else {
      console.log('Starting file listing process...\n');
      const files = await processor.listFiles(zipFile);
      
      console.log('\n=== FILE LISTING COMPLETED ===');
      console.log(`Total items found: ${files.length}`);
      
      // Show first 20 files as sample
      console.log('\nSample files (first 20):');
      files.slice(0, 20).forEach((file, index) => {
        const indent = '  '.repeat(file.depth || 0);
        const icon = file.isDirectory ? '📁' : file.isZip ? '[ZIP]' : '📄';
        const size = file.isDirectory ? '' : ` (${processor.formatFileSize(file.size)})`;
        console.log(`${index + 1:2}. ${indent}${icon} ${file.path}${size}`);
      });
      
      if (files.length > 20) {
        console.log(`... and ${files.length - 20} more files`);
      }
      
      // Show nested ZIP summary
      const nestedZips = files.filter(f => f.isZip);
      if (nestedZips.length > 0) {
        console.log(`\nNested ZIP files found: ${nestedZips.length}`);
        nestedZips.forEach(zip => {
          console.log(`  - ${zip.path} (${processor.formatFileSize(zip.size)}) at depth ${zip.depth}`);
        });
      }
    }
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    console.log(`\nTotal processing time: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}s)`);
    
    // Final memory usage
    const finalMemUsage = process.memoryUsage();
    console.log(`Final memory usage: ${processor.formatFileSize(finalMemUsage.heapUsed)}`);
    
  } catch (error) {
    console.error('\n❌ Error processing large file:', error.message);
    
    if (error.code === 'ERR_FS_FILE_TOO_LARGE') {
      console.error('\nThis error should not occur with the updated streaming reader.');
      console.error('Please check if the file is corrupted or if there are permission issues.');
    } else if (error.message.includes('out of memory') || error.message.includes('heap')) {
      console.error('\n💡 Memory suggestions:');
      console.error('1. Increase Node.js heap size: node --max-old-space-size=8192 large-file-test.js');
      console.error('2. Process with lower recursion depth: --max-depth 2');
      console.error('3. List files only (no extraction) to use less memory');
    } else if (error.message.includes('Maximum recursion depth')) {
      console.error('\n💡 Try reducing the recursion depth with --max-depth option');
    }
    
    process.exit(1);
  }
}

// Performance monitoring
function logMemoryUsage(label) {
  const usage = process.memoryUsage();
  console.log(`[${label}] Memory: ${formatBytes(usage.heapUsed)} / ${formatBytes(usage.heapTotal)}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the test
if (require.main === module) {
  testLargeFile().catch(console.error);
}

module.exports = { testLargeFile };
