const RecursiveZipProcessor = require('./unzip');

async function quickTest() {
  console.log('=== Quick Test for Manuals.zip ===\n');
  
  const processor = new RecursiveZipProcessor({
    maxDepth: 3, // Limit depth to avoid too much recursion
    maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB limit
    outputDir: './manuals-extracted'
  });

  try {
    console.log('Testing with your Manuals.zip file...');
    
    // First, just list the files to see what's inside
    const files = await processor.listFiles('./Manuals.zip');
    
    console.log(`\n✅ Success! Found ${files.length} items in the archive.`);
    
    // Show summary
    const summary = processor.generateSummary(files);
    console.log('\nSummary:');
    console.log(`- Total files: ${summary.totalFiles}`);
    console.log(`- Total directories: ${summary.totalDirectories}`);
    console.log(`- Nested ZIPs: ${summary.totalZips}`);
    console.log(`- Total size: ${summary.totalSize}`);
    console.log(`- Max depth: ${summary.maxDepth}`);
    
    // Show first 10 files
    console.log('\nFirst 10 items:');
    files.slice(0, 10).forEach((file, index) => {
      const indent = '  '.repeat(file.depth || 0);
      const icon = file.isDirectory ? '📁' : file.isZip ? '[ZIP]' : '📄';
      const size = file.isDirectory ? '' : ` (${processor.formatFileSize(file.size)})`;
      console.log(`${index + 1:2}. ${indent}${icon} ${file.name}${size}`);
    });
    
    if (files.length > 10) {
      console.log(`... and ${files.length - 10} more items`);
    }
    
    // Show nested ZIPs if any
    const nestedZips = files.filter(f => f.isZip);
    if (nestedZips.length > 0) {
      console.log(`\nFound ${nestedZips.length} nested ZIP files:`);
      nestedZips.forEach(zip => {
        console.log(`  🗜️ ${zip.path} (${processor.formatFileSize(zip.size)})`);
      });
    }
    
    console.log('\n🎉 The large file processing is now working!');
    console.log('\nTo extract files, run:');
    console.log('node large-file-test.js ./Manuals.zip --extract');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.message.includes('heap') || error.message.includes('memory')) {
      console.log('\n💡 Try running with more memory:');
      console.log('node --max-old-space-size=4096 quick-test.js');
    }
  }
}

quickTest();
