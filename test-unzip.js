const fs = require('fs').promises;
const path = require('path');
const RecursiveZipProcessor = require('./unzip');

// Create a test ZIP file with nested structure for testing
async function createTestZipFiles() {
  console.log('Creating test ZIP files...');
  
  // This is a simplified test - in a real scenario, you'd use a ZIP creation library
  // For now, we'll just test with existing ZIP files
  
  const testDir = './test-files';
  await fs.mkdir(testDir, { recursive: true });
  
  // Create some test files
  await fs.writeFile(path.join(testDir, 'test1.txt'), 'This is test file 1');
  await fs.writeFile(path.join(testDir, 'test2.txt'), 'This is test file 2');
  await fs.writeFile(path.join(testDir, 'readme.md'), '# Test README\nThis is a test markdown file');
  
  console.log('Test files created in ./test-files/');
  console.log('Note: You\'ll need to manually create ZIP files for full testing');
}

// Test the RecursiveZipProcessor functionality
async function runTests() {
  console.log('=== Recursive ZIP Processor Tests ===\n');
  
  const processor = new RecursiveZipProcessor({
    maxDepth: 5,
    maxFileSize: 1024 * 1024 * 1024, // 1GB for testing
    outputDir: './test-output'
  });

  // Test 1: Constructor and basic properties
  console.log('Test 1: Constructor');
  console.log(`Max depth: ${processor.maxDepth}`);
  console.log(`Max file size: ${processor.formatFileSize(processor.maxFileSize)}`);
  console.log(`Output directory: ${processor.outputDir}`);
  console.log('✓ Constructor test passed\n');

  // Test 2: File size formatting
  console.log('Test 2: File size formatting');
  const testSizes = [0, 1024, 1048576, 1073741824, 1099511627776];
  const expectedFormats = ['0 Bytes', '1 KB', '1 MB', '1 GB', '1 TB'];
  
  testSizes.forEach((size, index) => {
    const formatted = processor.formatFileSize(size);
    console.log(`${size} bytes = ${formatted}`);
    if (formatted === expectedFormats[index]) {
      console.log('✓ Size formatting correct');
    } else {
      console.log(`✗ Expected ${expectedFormats[index]}, got ${formatted}`);
    }
  });
  console.log();

  // Test 3: ZIP file detection
  console.log('Test 3: ZIP file detection');
  
  // Test with ZIP signature
  const zipSignature = new Uint8Array([0x50, 0x4B, 0x03, 0x04, 0x00, 0x00]);
  console.log(`ZIP signature detection: ${processor.isZipFile('test.zip', zipSignature)}`);
  console.log(`Extension detection: ${processor.isZipFile('archive.zip', new Uint8Array([0x00]))}`);
  console.log(`Non-ZIP detection: ${processor.isZipFile('test.txt', new Uint8Array([0x48, 0x65, 0x6C, 0x6C]))}`);
  console.log('✓ ZIP detection test completed\n');

  // Test 4: Summary generation
  console.log('Test 4: Summary generation');
  const mockFiles = [
    { isDirectory: false, isZip: false, size: 1024, depth: 0 },
    { isDirectory: false, isZip: true, size: 2048, depth: 0 },
    { isDirectory: true, isZip: false, size: 0, depth: 0 },
    { isDirectory: false, isZip: false, size: 512, depth: 1 },
    { isDirectory: false, isZip: false, size: 256, depth: 2 }
  ];
  
  const summary = processor.generateSummary(mockFiles);
  console.log('Generated summary:', JSON.stringify(summary, null, 2));
  console.log('✓ Summary generation test completed\n');

  // Test 5: Error handling
  console.log('Test 5: Error handling');
  try {
    await processor.processZipFile('nonexistent.zip');
    console.log('✗ Should have thrown error for nonexistent file');
  } catch (error) {
    console.log(`✓ Correctly caught error: ${error.message}`);
  }
  console.log();

  // Test 6: Real ZIP file processing (if available)
  console.log('Test 6: Real ZIP file processing');
  try {
    // Try to find any ZIP files in current directory
    const files = await fs.readdir('.');
    const zipFiles = files.filter(f => f.toLowerCase().endsWith('.zip'));
    
    if (zipFiles.length > 0) {
      const testZip = zipFiles[0];
      console.log(`Testing with: ${testZip}`);
      
      const result = await processor.processZipFile(testZip, { extract: false });
      console.log(`✓ Successfully processed ${testZip}`);
      console.log(`  - Files found: ${result.files.length}`);
      console.log(`  - Processing time: ${result.processingTime}ms`);
      console.log(`  - Summary:`, result.summary);
      
      // Test listing
      console.log('\nFile listing:');
      result.files.slice(0, 10).forEach(file => {
        const indent = '  '.repeat(file.depth || 0);
        const icon = file.isDirectory ? '📁' : file.isZip ? '[ZIP]' : '📄';
        const size = file.isDirectory ? '' : ` (${processor.formatFileSize(file.size)})`;
        console.log(`${indent}${icon} ${file.path}${size}`);
      });
      
      if (result.files.length > 10) {
        console.log(`... and ${result.files.length - 10} more files`);
      }
      
    } else {
      console.log('No ZIP files found in current directory for testing');
      console.log('Create a test ZIP file to run full tests');
    }
  } catch (error) {
    console.log(`Error during real file test: ${error.message}`);
  }
  
  console.log('\n=== Tests Completed ===');
}

// Performance test
async function performanceTest() {
  console.log('\n=== Performance Test ===');
  
  const processor = new RecursiveZipProcessor();
  
  // Test processing speed with different configurations
  const configs = [
    { maxDepth: 1, name: 'Shallow (depth 1)' },
    { maxDepth: 5, name: 'Medium (depth 5)' },
    { maxDepth: 10, name: 'Deep (depth 10)' }
  ];
  
  for (const config of configs) {
    processor.maxDepth = config.maxDepth;
    console.log(`\nTesting ${config.name}:`);
    
    const startTime = Date.now();
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));
    const endTime = Date.now();
    
    console.log(`Configuration overhead: ${endTime - startTime}ms`);
  }
}

// Main test runner
async function main() {
  try {
    await createTestZipFiles();
    await runTests();
    await performanceTest();
    
    console.log('\n=== All Tests Completed Successfully ===');
    console.log('\nTo test with real ZIP files:');
    console.log('1. Place ZIP files in the current directory');
    console.log('2. Run: node test-unzip.js');
    console.log('3. Or use CLI: node unzip.js your-file.zip --list');
    
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  createTestZipFiles,
  runTests,
  performanceTest
};
