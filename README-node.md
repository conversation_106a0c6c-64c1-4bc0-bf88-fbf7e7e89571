# Recursive ZIP Processor (Node.js)

A powerful Node.js library for recursively processing ZIP files, including nested ZIP archives. Built with the high-performance `fflate` library for efficient ZIP decompression.

## Features

- 🗜️ **Recursive ZIP Processing** - Automatically detects and processes ZIP files within ZIP files
- 📦 **Large File Support** - Handles ZIP files up to 5GB (configurable)
- 🔍 **Smart ZIP Detection** - Uses both file extensions and magic byte signatures
- 📋 **Comprehensive File Listing** - Detailed information about all files and directories
- 📊 **Processing Statistics** - File counts, sizes, and processing metrics
- ⬇️ **Selective Extraction** - Extract all files or process programmatically
- 🧭 **Depth Control** - Configurable maximum recursion depth
- 🎯 **Flexible Output** - Preserve directory structure or flatten files
- 🚀 **High Performance** - Built on fflate for fast decompression
- 📝 **CLI Interface** - Command-line tool for quick operations

## Installation

```bash
npm install fflate
```

## Quick Start

### Command Line Usage

```bash
# List all files in a ZIP (including nested ZIPs)
node unzip.js example.zip --list

# Extract all files
node unzip.js example.zip --extract

# Extract to specific directory
node unzip.js example.zip --extract --output ./my-files

# Set maximum recursion depth
node unzip.js example.zip --extract --max-depth 5
```

### Programmatic Usage

```javascript
const RecursiveZipProcessor = require('./unzip');

// Create processor with options
const processor = new RecursiveZipProcessor({
  maxDepth: 10,
  maxFileSize: 5 * 1024 * 1024 * 1024, // 5GB
  outputDir: './extracted',
  preserveStructure: true
});

// List files only
const files = await processor.listFiles('example.zip');

// Extract all files
const result = await processor.processZipFile('example.zip', {
  extract: true,
  outputDir: './my-output'
});
```

## API Reference

### Constructor Options

```javascript
const processor = new RecursiveZipProcessor({
  maxDepth: 10,           // Maximum recursion depth (default: 10)
  maxFileSize: 5GB,       // Maximum file size in bytes (default: 5GB)
  outputDir: './extracted', // Default output directory
  preserveStructure: true  // Preserve directory structure (default: true)
});
```

### Methods

#### `processZipFile(zipFilePath, options)`

Main method to process a ZIP file recursively.

**Parameters:**
- `zipFilePath` (string) - Path to the ZIP file
- `options` (object) - Processing options
  - `extract` (boolean) - Whether to extract files (default: false)
  - `outputDir` (string) - Output directory for extraction

**Returns:** Promise resolving to:
```javascript
{
  files: [...],        // Array of file objects
  summary: {...},      // Processing summary
  processingTime: 1234 // Processing time in milliseconds
}
```

#### `listFiles(zipFilePath)`

List all files without extracting.

**Returns:** Promise resolving to array of file objects.

#### `extractFiles(files, outputDir)`

Extract specific files to filesystem.

### File Object Structure

```javascript
{
  path: 'folder/file.txt',      // Full path including nested ZIP paths
  originalPath: 'file.txt',     // Original path within immediate parent ZIP
  name: 'file.txt',             // File name only
  size: 1024,                   // File size in bytes
  isDirectory: false,           // Whether this is a directory
  isZip: false,                 // Whether this is a ZIP file
  data: Uint8Array,             // File data (null for directories)
  parentZip: 'archive.zip',     // Name of containing ZIP
  depth: 0                      // Nesting depth (0 = root ZIP)
}
```

## Examples

### Example 1: Basic File Listing

```javascript
const processor = new RecursiveZipProcessor();

try {
  const files = await processor.listFiles('archive.zip');
  
  console.log(`Found ${files.length} items:`);
  files.forEach(file => {
    const indent = '  '.repeat(file.depth);
    const icon = file.isDirectory ? '📁' : file.isZip ? '[ZIP]' : '📄';
    console.log(`${indent}${icon} ${file.path}`);
  });
} catch (error) {
  console.error('Error:', error.message);
}
```

### Example 2: Extract with Custom Processing

```javascript
const processor = new RecursiveZipProcessor({
  outputDir: './extracted-files'
});

try {
  const result = await processor.processZipFile('archive.zip', {
    extract: true
  });
  
  console.log('Summary:', result.summary);
  
  // Find all nested ZIP files
  const nestedZips = result.files.filter(f => f.isZip);
  console.log(`Found ${nestedZips.length} nested ZIP files`);
  
} catch (error) {
  console.error('Error:', error.message);
}
```

### Example 3: Selective File Processing

```javascript
const processor = new RecursiveZipProcessor();

try {
  const result = await processor.processZipFile('archive.zip', { extract: false });
  
  // Filter specific file types
  const images = result.files.filter(f => 
    !f.isDirectory && /\.(jpg|png|gif)$/i.test(f.name)
  );
  
  // Extract only image files
  await processor.extractFiles(images, './images-only');
  
} catch (error) {
  console.error('Error:', error.message);
}
```

## Command Line Options

```bash
node unzip.js <zip-file> [options]

Options:
  --extract, -e          Extract files to filesystem
  --output, -o <dir>     Output directory (default: ./extracted)
  --max-depth <n>        Maximum recursion depth (default: 10)
  --max-size <bytes>     Maximum file size in bytes (default: 5GB)
  --list, -l             List files only (no extraction)

Examples:
  node unzip.js archive.zip --list
  node unzip.js archive.zip --extract --output ./my-files
  node unzip.js archive.zip -e -o ./extracted --max-depth 5
```

## Processing Summary

The processor provides detailed statistics:

```javascript
{
  totalFiles: 150,           // Number of files (excluding directories)
  totalDirectories: 25,      // Number of directories
  totalZips: 5,              // Number of nested ZIP files found
  totalSize: "45.2 MB",      // Total uncompressed size
  maxDepth: 3,               // Maximum nesting depth found
  filesByDepth: {            // File count by depth level
    0: 100,                  // 100 files in root ZIP
    1: 35,                   // 35 files in first-level nested ZIPs
    2: 15                    // 15 files in second-level nested ZIPs
  }
}
```

## Error Handling

The processor includes comprehensive error handling:

- **File size limits** - Prevents processing files exceeding size limits
- **Recursion depth limits** - Prevents infinite recursion
- **Corrupted ZIP handling** - Graceful handling of invalid ZIP files
- **File system errors** - Proper error reporting for I/O operations

## Performance Considerations

- **Memory Usage** - Files are processed in memory; ensure sufficient RAM for large archives
- **Streaming** - Uses fflate's efficient streaming decompression
- **Parallel Processing** - Nested ZIPs are processed sequentially to manage memory
- **Large Files** - Consider increasing Node.js memory limit for very large archives:
  ```bash
  node --max-old-space-size=8192 unzip.js large-archive.zip
  ```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create your feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Changelog

### v1.0.0
- Initial release with recursive ZIP processing
- CLI interface
- Comprehensive file information
- Configurable extraction options
