{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\BrowserZip Decompressor and list\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from \"react\";\nimport { unzip } from \"fflate\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [files, setFiles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [progress, setProgress] = useState(0);\n  const [zipInfo, setZipInfo] = useState(null);\n  const [zipStack, setZipStack] = useState([]); // Stack for nested ZIP navigation\n  const [currentPath, setCurrentPath] = useState(\"\"); // Current path in nested ZIPs\n\n  const formatFileSize = bytes => {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  };\n  const isZipFile = (filename, data) => {\n    if (!filename || !data) return false;\n\n    // Check file extension\n    const isZipExtension = filename.toLowerCase().endsWith(\".zip\");\n\n    // Check ZIP file signature (magic bytes)\n    const hasZipSignature = data.length >= 4 && data[0] === 0x50 && data[1] === 0x4b && (data[2] === 0x03 || data[2] === 0x05 || data[2] === 0x07) && (data[3] === 0x04 || data[3] === 0x06 || data[3] === 0x08);\n    return isZipExtension || hasZipSignature;\n  };\n  const processZipData = useCallback((zipData, zipName, parentPath = \"\") => {\n    return new Promise((resolve, reject) => {\n      unzip(zipData, (err, unzipped) => {\n        if (err) {\n          reject(new Error(`Error reading ZIP file: ${err.message}`));\n          return;\n        }\n        const fileList = [];\n        let totalFiles = 0;\n        let totalSize = 0;\n        for (const [path, data] of Object.entries(unzipped)) {\n          const isDirectory = path.endsWith(\"/\");\n          const fullPath = parentPath ? `${parentPath}/${path}` : path;\n          if (!isDirectory) {\n            totalFiles++;\n            totalSize += data.length;\n          }\n          const isNestedZip = !isDirectory && isZipFile(path, data);\n          fileList.push({\n            path: fullPath,\n            originalPath: path,\n            name: path.split(\"/\").pop() || path,\n            size: isDirectory ? 0 : data.length,\n            isDirectory,\n            isZip: isNestedZip,\n            data: isDirectory ? null : data,\n            parentZip: zipName\n          });\n        }\n\n        // Sort files: directories first, then files, both alphabetically\n        fileList.sort((a, b) => {\n          if (a.isDirectory && !b.isDirectory) return -1;\n          if (!a.isDirectory && b.isDirectory) return 1;\n          return a.path.localeCompare(b.path);\n        });\n        resolve({\n          fileList,\n          totalFiles,\n          totalSize\n        });\n      });\n    });\n  }, []);\n  const exploreNestedZip = useCallback(async file => {\n    if (!file.isZip || !file.data) return;\n    setLoading(true);\n    setError(null);\n    try {\n      const result = await processZipData(file.data, file.name, file.path);\n\n      // Add current state to stack for navigation\n      setZipStack(prev => [...prev, {\n        files,\n        zipInfo,\n        path: currentPath\n      }]);\n      setFiles(result.fileList);\n      setZipInfo({\n        name: file.name,\n        size: file.size,\n        totalFiles: result.totalFiles,\n        totalSize: result.totalSize,\n        isNested: true\n      });\n      setCurrentPath(file.path);\n      setLoading(false);\n    } catch (err) {\n      setError(`Error processing nested ZIP: ${err.message}`);\n      setLoading(false);\n    }\n  }, [files, zipInfo, currentPath, processZipData]);\n  const goBack = useCallback(() => {\n    if (zipStack.length === 0) return;\n    const previousState = zipStack[zipStack.length - 1];\n    setFiles(previousState.files);\n    setZipInfo(previousState.zipInfo);\n    setCurrentPath(previousState.path);\n    setZipStack(prev => prev.slice(0, -1));\n  }, [zipStack]);\n  const handleFileUpload = useCallback(async event => {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (!file.name.toLowerCase().endsWith(\".zip\")) {\n      setError(\"Please select a ZIP file\");\n      return;\n    }\n\n    // Check file size limit (3GB)\n    const maxSize = 5 * 1024 * 1024 * 1024; // 3GB in bytes\n    if (file.size > maxSize) {\n      setError(\"File size exceeds 3GB limit\");\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setFiles([]);\n    setProgress(0);\n    setZipInfo({\n      name: file.name,\n      size: file.size\n    });\n\n    // Reset navigation state\n    setZipStack([]);\n    setCurrentPath(\"\");\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const uint8Array = new Uint8Array(arrayBuffer);\n      const result = await processZipData(uint8Array, file.name);\n      setFiles(result.fileList);\n      setZipInfo(prev => ({\n        ...prev,\n        totalFiles: result.totalFiles,\n        totalSize: result.totalSize,\n        isNested: false\n      }));\n      setLoading(false);\n      setProgress(100);\n    } catch (err) {\n      setError(`Error processing file: ${err.message}`);\n      setLoading(false);\n    }\n  }, [processZipData]);\n  const downloadFile = file => {\n    if (file.isDirectory || !file.data) return;\n    const blob = new Blob([file.data]);\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = file.name;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDDDC\\uFE0F BrowserZip Decompressor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload and explore ZIP files entirely in your browser\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"App-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-area\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            accept: \".zip\",\n            onChange: handleFileUpload,\n            disabled: loading,\n            id: \"zip-upload\",\n            className: \"file-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"zip-upload\",\n            className: `upload-label ${loading ? \"disabled\" : \"\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-icon\",\n              children: \"\\uD83D\\uDCC1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-text\",\n              children: loading ? \"Processing...\" : \"Choose ZIP file or drag & drop\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-hint\",\n              children: \"Supports files up to 3GB with recursive ZIP exploration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Processing ZIP file...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [\"\\u274C \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), zipInfo && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"zip-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"zip-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCE6 ZIP File Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), zipInfo.isNested && zipStack.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"back-btn\",\n            onClick: goBack,\n            children: \"\\u2190 Back to Parent ZIP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), currentPath && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"current-path\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Current Path:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 17\n          }, this), \" \", currentPath]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"File Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), \" \", zipInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Archive Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), \" \", formatFileSize(zipInfo.size)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total Files:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), \" \", zipInfo.totalFiles]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Uncompressed Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), \" \", formatFileSize(zipInfo.totalSize)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), zipInfo.isNested && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this), \" Nested ZIP Archive\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"files-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"\\uD83D\\uDCCB Files in Archive (\", files.length, \" items)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"files-list\",\n          children: files.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `file-item ${file.isDirectory ? \"directory\" : file.isZip ? \"zip-file\" : \"file\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-icon\",\n              children: file.isDirectory ? \"📁\" : file.isZip ? \"[ZIP]\" : \"📄\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-path\",\n                children: file.path\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-meta\",\n                children: !file.isDirectory && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-size\",\n                    children: formatFileSize(file.size)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 27\n                  }, this), file.isZip && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"explore-btn\",\n                    onClick: () => exploreNestedZip(file),\n                    title: \"Explore nested ZIP\",\n                    children: \"\\uD83D\\uDD0D Explore ZIP\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"download-btn\",\n                    onClick: () => downloadFile(file),\n                    title: \"Download file\",\n                    children: \"\\u2B07\\uFE0F Download\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"raJKrzHQKoG1XJecENKdzbASOSo=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "unzip", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "files", "setFiles", "loading", "setLoading", "error", "setError", "progress", "setProgress", "zipInfo", "setZipInfo", "zipStack", "setZipStack", "currentPath", "setCurrentPath", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "isZipFile", "filename", "data", "isZipExtension", "toLowerCase", "endsWith", "hasZipSignature", "length", "processZipData", "zipData", "zipName", "parentPath", "Promise", "resolve", "reject", "err", "unzipped", "Error", "message", "fileList", "totalFiles", "totalSize", "path", "Object", "entries", "isDirectory", "fullPath", "isNestedZip", "push", "originalPath", "name", "split", "pop", "size", "isZip", "parentZip", "sort", "a", "b", "localeCompare", "exploreNestedZip", "file", "result", "prev", "isNested", "goBack", "previousState", "slice", "handleFileUpload", "event", "target", "maxSize", "arrayBuffer", "uint8Array", "Uint8Array", "downloadFile", "blob", "Blob", "url", "URL", "createObjectURL", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "accept", "onChange", "disabled", "id", "htmlFor", "style", "width", "onClick", "map", "index", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/BrowserZip Decompressor and list/src/App.js"], "sourcesContent": ["import React, { useState, useCallback } from \"react\";\nimport { unzip } from \"fflate\";\nimport \"./App.css\";\n\nfunction App() {\n  const [files, setFiles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [progress, setProgress] = useState(0);\n  const [zipInfo, setZipInfo] = useState(null);\n  const [zipStack, setZipStack] = useState([]); // Stack for nested ZIP navigation\n  const [currentPath, setCurrentPath] = useState(\"\"); // Current path in nested ZIPs\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  };\n\n  const isZipFile = (filename, data) => {\n    if (!filename || !data) return false;\n\n    // Check file extension\n    const isZipExtension = filename.toLowerCase().endsWith(\".zip\");\n\n    // Check ZIP file signature (magic bytes)\n    const hasZipSignature =\n      data.length >= 4 &&\n      data[0] === 0x50 &&\n      data[1] === 0x4b &&\n      (data[2] === 0x03 || data[2] === 0x05 || data[2] === 0x07) &&\n      (data[3] === 0x04 || data[3] === 0x06 || data[3] === 0x08);\n\n    return isZipExtension || hasZipSignature;\n  };\n\n  const processZipData = useCallback((zipData, zipName, parentPath = \"\") => {\n    return new Promise((resolve, reject) => {\n      unzip(zipData, (err, unzipped) => {\n        if (err) {\n          reject(new Error(`Error reading ZIP file: ${err.message}`));\n          return;\n        }\n\n        const fileList = [];\n        let totalFiles = 0;\n        let totalSize = 0;\n\n        for (const [path, data] of Object.entries(unzipped)) {\n          const isDirectory = path.endsWith(\"/\");\n          const fullPath = parentPath ? `${parentPath}/${path}` : path;\n\n          if (!isDirectory) {\n            totalFiles++;\n            totalSize += data.length;\n          }\n\n          const isNestedZip = !isDirectory && isZipFile(path, data);\n\n          fileList.push({\n            path: fullPath,\n            originalPath: path,\n            name: path.split(\"/\").pop() || path,\n            size: isDirectory ? 0 : data.length,\n            isDirectory,\n            isZip: isNestedZip,\n            data: isDirectory ? null : data,\n            parentZip: zipName,\n          });\n        }\n\n        // Sort files: directories first, then files, both alphabetically\n        fileList.sort((a, b) => {\n          if (a.isDirectory && !b.isDirectory) return -1;\n          if (!a.isDirectory && b.isDirectory) return 1;\n          return a.path.localeCompare(b.path);\n        });\n\n        resolve({ fileList, totalFiles, totalSize });\n      });\n    });\n  }, []);\n\n  const exploreNestedZip = useCallback(\n    async (file) => {\n      if (!file.isZip || !file.data) return;\n\n      setLoading(true);\n      setError(null);\n\n      try {\n        const result = await processZipData(file.data, file.name, file.path);\n\n        // Add current state to stack for navigation\n        setZipStack((prev) => [\n          ...prev,\n          {\n            files,\n            zipInfo,\n            path: currentPath,\n          },\n        ]);\n\n        setFiles(result.fileList);\n        setZipInfo({\n          name: file.name,\n          size: file.size,\n          totalFiles: result.totalFiles,\n          totalSize: result.totalSize,\n          isNested: true,\n        });\n        setCurrentPath(file.path);\n        setLoading(false);\n      } catch (err) {\n        setError(`Error processing nested ZIP: ${err.message}`);\n        setLoading(false);\n      }\n    },\n    [files, zipInfo, currentPath, processZipData]\n  );\n\n  const goBack = useCallback(() => {\n    if (zipStack.length === 0) return;\n\n    const previousState = zipStack[zipStack.length - 1];\n    setFiles(previousState.files);\n    setZipInfo(previousState.zipInfo);\n    setCurrentPath(previousState.path);\n    setZipStack((prev) => prev.slice(0, -1));\n  }, [zipStack]);\n\n  const handleFileUpload = useCallback(\n    async (event) => {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      if (!file.name.toLowerCase().endsWith(\".zip\")) {\n        setError(\"Please select a ZIP file\");\n        return;\n      }\n\n      // Check file size limit (3GB)\n      const maxSize = 5 * 1024 * 1024 * 1024; // 3GB in bytes\n      if (file.size > maxSize) {\n        setError(\"File size exceeds 3GB limit\");\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n      setFiles([]);\n      setProgress(0);\n      setZipInfo({ name: file.name, size: file.size });\n\n      // Reset navigation state\n      setZipStack([]);\n      setCurrentPath(\"\");\n\n      try {\n        const arrayBuffer = await file.arrayBuffer();\n        const uint8Array = new Uint8Array(arrayBuffer);\n\n        const result = await processZipData(uint8Array, file.name);\n\n        setFiles(result.fileList);\n        setZipInfo((prev) => ({\n          ...prev,\n          totalFiles: result.totalFiles,\n          totalSize: result.totalSize,\n          isNested: false,\n        }));\n        setLoading(false);\n        setProgress(100);\n      } catch (err) {\n        setError(`Error processing file: ${err.message}`);\n        setLoading(false);\n      }\n    },\n    [processZipData]\n  );\n\n  const downloadFile = (file) => {\n    if (file.isDirectory || !file.data) return;\n\n    const blob = new Blob([file.data]);\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = file.name;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>🗜️ BrowserZip Decompressor</h1>\n        <p>Upload and explore ZIP files entirely in your browser</p>\n      </header>\n\n      <main className=\"App-main\">\n        <div className=\"upload-section\">\n          <div className=\"upload-area\">\n            <input\n              type=\"file\"\n              accept=\".zip\"\n              onChange={handleFileUpload}\n              disabled={loading}\n              id=\"zip-upload\"\n              className=\"file-input\"\n            />\n            <label\n              htmlFor=\"zip-upload\"\n              className={`upload-label ${loading ? \"disabled\" : \"\"}`}\n            >\n              <div className=\"upload-icon\">📁</div>\n              <div className=\"upload-text\">\n                {loading ? \"Processing...\" : \"Choose ZIP file or drag & drop\"}\n              </div>\n              <div className=\"upload-hint\">\n                Supports files up to 3GB with recursive ZIP exploration\n              </div>\n            </label>\n          </div>\n\n          {loading && (\n            <div className=\"progress-section\">\n              <div className=\"progress-bar\">\n                <div\n                  className=\"progress-fill\"\n                  style={{ width: `${progress}%` }}\n                ></div>\n              </div>\n              <p>Processing ZIP file...</p>\n            </div>\n          )}\n\n          {error && <div className=\"error-message\">❌ {error}</div>}\n        </div>\n\n        {zipInfo && !loading && (\n          <div className=\"zip-info\">\n            <div className=\"zip-header\">\n              <h3>📦 ZIP File Information</h3>\n              {zipInfo.isNested && zipStack.length > 0 && (\n                <button className=\"back-btn\" onClick={goBack}>\n                  ← Back to Parent ZIP\n                </button>\n              )}\n            </div>\n\n            {currentPath && (\n              <div className=\"current-path\">\n                <strong>Current Path:</strong> {currentPath}\n              </div>\n            )}\n\n            <div className=\"info-grid\">\n              <div className=\"info-item\">\n                <strong>File Name:</strong> {zipInfo.name}\n              </div>\n              <div className=\"info-item\">\n                <strong>Archive Size:</strong> {formatFileSize(zipInfo.size)}\n              </div>\n              <div className=\"info-item\">\n                <strong>Total Files:</strong> {zipInfo.totalFiles}\n              </div>\n              <div className=\"info-item\">\n                <strong>Uncompressed Size:</strong>{\" \"}\n                {formatFileSize(zipInfo.totalSize)}\n              </div>\n              {zipInfo.isNested && (\n                <div className=\"info-item\">\n                  <strong>Type:</strong> Nested ZIP Archive\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {files.length > 0 && (\n          <div className=\"files-section\">\n            <h3>📋 Files in Archive ({files.length} items)</h3>\n            <div className=\"files-list\">\n              {files.map((file, index) => (\n                <div\n                  key={index}\n                  className={`file-item ${\n                    file.isDirectory\n                      ? \"directory\"\n                      : file.isZip\n                      ? \"zip-file\"\n                      : \"file\"\n                  }`}\n                >\n                  <div className=\"file-icon\">\n                    {file.isDirectory ? \"📁\" : file.isZip ? \"[ZIP]\" : \"📄\"}\n                  </div>\n                  <div className=\"file-details\">\n                    <div className=\"file-path\">{file.path}</div>\n                    <div className=\"file-meta\">\n                      {!file.isDirectory && (\n                        <>\n                          <span className=\"file-size\">\n                            {formatFileSize(file.size)}\n                          </span>\n                          {file.isZip && (\n                            <button\n                              className=\"explore-btn\"\n                              onClick={() => exploreNestedZip(file)}\n                              title=\"Explore nested ZIP\"\n                            >\n                              🔍 Explore ZIP\n                            </button>\n                          )}\n                          <button\n                            className=\"download-btn\"\n                            onClick={() => downloadFile(file)}\n                            title=\"Download file\"\n                          >\n                            ⬇️ Download\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAASC,KAAK,QAAQ,QAAQ;AAC9B,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD,MAAMuB,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,SAAS,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACpC,IAAI,CAACD,QAAQ,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;;IAEpC;IACA,MAAMC,cAAc,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC;;IAE9D;IACA,MAAMC,eAAe,GACnBJ,IAAI,CAACK,MAAM,IAAI,CAAC,IAChBL,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAChBA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,KACfA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KACzDA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAE5D,OAAOC,cAAc,IAAIG,eAAe;EAC1C,CAAC;EAED,MAAME,cAAc,GAAGzC,WAAW,CAAC,CAAC0C,OAAO,EAAEC,OAAO,EAAEC,UAAU,GAAG,EAAE,KAAK;IACxE,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC9C,KAAK,CAACyC,OAAO,EAAE,CAACM,GAAG,EAAEC,QAAQ,KAAK;QAChC,IAAID,GAAG,EAAE;UACPD,MAAM,CAAC,IAAIG,KAAK,CAAC,2BAA2BF,GAAG,CAACG,OAAO,EAAE,CAAC,CAAC;UAC3D;QACF;QAEA,MAAMC,QAAQ,GAAG,EAAE;QACnB,IAAIC,UAAU,GAAG,CAAC;QAClB,IAAIC,SAAS,GAAG,CAAC;QAEjB,KAAK,MAAM,CAACC,IAAI,EAAEpB,IAAI,CAAC,IAAIqB,MAAM,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;UACnD,MAAMS,WAAW,GAAGH,IAAI,CAACjB,QAAQ,CAAC,GAAG,CAAC;UACtC,MAAMqB,QAAQ,GAAGf,UAAU,GAAG,GAAGA,UAAU,IAAIW,IAAI,EAAE,GAAGA,IAAI;UAE5D,IAAI,CAACG,WAAW,EAAE;YAChBL,UAAU,EAAE;YACZC,SAAS,IAAInB,IAAI,CAACK,MAAM;UAC1B;UAEA,MAAMoB,WAAW,GAAG,CAACF,WAAW,IAAIzB,SAAS,CAACsB,IAAI,EAAEpB,IAAI,CAAC;UAEzDiB,QAAQ,CAACS,IAAI,CAAC;YACZN,IAAI,EAAEI,QAAQ;YACdG,YAAY,EAAEP,IAAI;YAClBQ,IAAI,EAAER,IAAI,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,IAAIV,IAAI;YACnCW,IAAI,EAAER,WAAW,GAAG,CAAC,GAAGvB,IAAI,CAACK,MAAM;YACnCkB,WAAW;YACXS,KAAK,EAAEP,WAAW;YAClBzB,IAAI,EAAEuB,WAAW,GAAG,IAAI,GAAGvB,IAAI;YAC/BiC,SAAS,EAAEzB;UACb,CAAC,CAAC;QACJ;;QAEA;QACAS,QAAQ,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACtB,IAAID,CAAC,CAACZ,WAAW,IAAI,CAACa,CAAC,CAACb,WAAW,EAAE,OAAO,CAAC,CAAC;UAC9C,IAAI,CAACY,CAAC,CAACZ,WAAW,IAAIa,CAAC,CAACb,WAAW,EAAE,OAAO,CAAC;UAC7C,OAAOY,CAAC,CAACf,IAAI,CAACiB,aAAa,CAACD,CAAC,CAAChB,IAAI,CAAC;QACrC,CAAC,CAAC;QAEFT,OAAO,CAAC;UAAEM,QAAQ;UAAEC,UAAU;UAAEC;QAAU,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,gBAAgB,GAAGzE,WAAW,CAClC,MAAO0E,IAAI,IAAK;IACd,IAAI,CAACA,IAAI,CAACP,KAAK,IAAI,CAACO,IAAI,CAACvC,IAAI,EAAE;IAE/BxB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAM8D,MAAM,GAAG,MAAMlC,cAAc,CAACiC,IAAI,CAACvC,IAAI,EAAEuC,IAAI,CAACX,IAAI,EAAEW,IAAI,CAACnB,IAAI,CAAC;;MAEpE;MACApC,WAAW,CAAEyD,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEpE,KAAK;QACLQ,OAAO;QACPuC,IAAI,EAAEnC;MACR,CAAC,CACF,CAAC;MAEFX,QAAQ,CAACkE,MAAM,CAACvB,QAAQ,CAAC;MACzBnC,UAAU,CAAC;QACT8C,IAAI,EAAEW,IAAI,CAACX,IAAI;QACfG,IAAI,EAAEQ,IAAI,CAACR,IAAI;QACfb,UAAU,EAAEsB,MAAM,CAACtB,UAAU;QAC7BC,SAAS,EAAEqB,MAAM,CAACrB,SAAS;QAC3BuB,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFxD,cAAc,CAACqD,IAAI,CAACnB,IAAI,CAAC;MACzB5C,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACZnC,QAAQ,CAAC,gCAAgCmC,GAAG,CAACG,OAAO,EAAE,CAAC;MACvDxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EACD,CAACH,KAAK,EAAEQ,OAAO,EAAEI,WAAW,EAAEqB,cAAc,CAC9C,CAAC;EAED,MAAMqC,MAAM,GAAG9E,WAAW,CAAC,MAAM;IAC/B,IAAIkB,QAAQ,CAACsB,MAAM,KAAK,CAAC,EAAE;IAE3B,MAAMuC,aAAa,GAAG7D,QAAQ,CAACA,QAAQ,CAACsB,MAAM,GAAG,CAAC,CAAC;IACnD/B,QAAQ,CAACsE,aAAa,CAACvE,KAAK,CAAC;IAC7BS,UAAU,CAAC8D,aAAa,CAAC/D,OAAO,CAAC;IACjCK,cAAc,CAAC0D,aAAa,CAACxB,IAAI,CAAC;IAClCpC,WAAW,CAAEyD,IAAI,IAAKA,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC,EAAE,CAAC9D,QAAQ,CAAC,CAAC;EAEd,MAAM+D,gBAAgB,GAAGjF,WAAW,CAClC,MAAOkF,KAAK,IAAK;IACf,MAAMR,IAAI,GAAGQ,KAAK,CAACC,MAAM,CAAC3E,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACkE,IAAI,EAAE;IAEX,IAAI,CAACA,IAAI,CAACX,IAAI,CAAC1B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7CzB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;;IAEA;IACA,MAAMuE,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACxC,IAAIV,IAAI,CAACR,IAAI,GAAGkB,OAAO,EAAE;MACvBvE,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdJ,QAAQ,CAAC,EAAE,CAAC;IACZM,WAAW,CAAC,CAAC,CAAC;IACdE,UAAU,CAAC;MAAE8C,IAAI,EAAEW,IAAI,CAACX,IAAI;MAAEG,IAAI,EAAEQ,IAAI,CAACR;IAAK,CAAC,CAAC;;IAEhD;IACA/C,WAAW,CAAC,EAAE,CAAC;IACfE,cAAc,CAAC,EAAE,CAAC;IAElB,IAAI;MACF,MAAMgE,WAAW,GAAG,MAAMX,IAAI,CAACW,WAAW,CAAC,CAAC;MAC5C,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACF,WAAW,CAAC;MAE9C,MAAMV,MAAM,GAAG,MAAMlC,cAAc,CAAC6C,UAAU,EAAEZ,IAAI,CAACX,IAAI,CAAC;MAE1DtD,QAAQ,CAACkE,MAAM,CAACvB,QAAQ,CAAC;MACzBnC,UAAU,CAAE2D,IAAI,KAAM;QACpB,GAAGA,IAAI;QACPvB,UAAU,EAAEsB,MAAM,CAACtB,UAAU;QAC7BC,SAAS,EAAEqB,MAAM,CAACrB,SAAS;QAC3BuB,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;MACHlE,UAAU,CAAC,KAAK,CAAC;MACjBI,WAAW,CAAC,GAAG,CAAC;IAClB,CAAC,CAAC,OAAOiC,GAAG,EAAE;MACZnC,QAAQ,CAAC,0BAA0BmC,GAAG,CAACG,OAAO,EAAE,CAAC;MACjDxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EACD,CAAC8B,cAAc,CACjB,CAAC;EAED,MAAM+C,YAAY,GAAId,IAAI,IAAK;IAC7B,IAAIA,IAAI,CAAChB,WAAW,IAAI,CAACgB,IAAI,CAACvC,IAAI,EAAE;IAEpC,MAAMsD,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAChB,IAAI,CAACvC,IAAI,CAAC,CAAC;IAClC,MAAMwD,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMnB,CAAC,GAAGwB,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCzB,CAAC,CAAC0B,IAAI,GAAGL,GAAG;IACZrB,CAAC,CAAC2B,QAAQ,GAAGvB,IAAI,CAACX,IAAI;IACtB+B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC7B,CAAC,CAAC;IAC5BA,CAAC,CAAC8B,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAAC/B,CAAC,CAAC;IAC5BsB,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,oBACExF,OAAA;IAAKoG,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBrG,OAAA;MAAQoG,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5BrG,OAAA;QAAAqG,QAAA,EAAI;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCzG,OAAA;QAAAqG,QAAA,EAAG;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAETzG,OAAA;MAAMoG,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACxBrG,OAAA;QAAKoG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrG,OAAA;UAAKoG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrG,OAAA;YACE0G,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,MAAM;YACbC,QAAQ,EAAE9B,gBAAiB;YAC3B+B,QAAQ,EAAEtG,OAAQ;YAClBuG,EAAE,EAAC,YAAY;YACfV,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACFzG,OAAA;YACE+G,OAAO,EAAC,YAAY;YACpBX,SAAS,EAAE,gBAAgB7F,OAAO,GAAG,UAAU,GAAG,EAAE,EAAG;YAAA8F,QAAA,gBAEvDrG,OAAA;cAAKoG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCzG,OAAA;cAAKoG,SAAS,EAAC,aAAa;cAAAC,QAAA,EACzB9F,OAAO,GAAG,eAAe,GAAG;YAAgC;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNzG,OAAA;cAAKoG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAELlG,OAAO,iBACNP,OAAA;UAAKoG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrG,OAAA;YAAKoG,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BrG,OAAA;cACEoG,SAAS,EAAC,eAAe;cACzBY,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGtG,QAAQ;cAAI;YAAE;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzG,OAAA;YAAAqG,QAAA,EAAG;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACN,EAEAhG,KAAK,iBAAIT,OAAA;UAAKoG,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,SAAE,EAAC5F,KAAK;QAAA;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,EAEL5F,OAAO,IAAI,CAACN,OAAO,iBAClBP,OAAA;QAAKoG,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBrG,OAAA;UAAKoG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrG,OAAA;YAAAqG,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC/B5F,OAAO,CAAC6D,QAAQ,IAAI3D,QAAQ,CAACsB,MAAM,GAAG,CAAC,iBACtCrC,OAAA;YAAQoG,SAAS,EAAC,UAAU;YAACc,OAAO,EAAEvC,MAAO;YAAA0B,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELxF,WAAW,iBACVjB,OAAA;UAAKoG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrG,OAAA;YAAAqG,QAAA,EAAQ;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxF,WAAW;QAAA;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACN,eAEDzG,OAAA;UAAKoG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrG,OAAA;YAAKoG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrG,OAAA;cAAAqG,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5F,OAAO,CAAC+C,IAAI;UAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNzG,OAAA;YAAKoG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrG,OAAA;cAAAqG,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACtF,cAAc,CAACN,OAAO,CAACkD,IAAI,CAAC;UAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNzG,OAAA;YAAKoG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrG,OAAA;cAAAqG,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5F,OAAO,CAACqC,UAAU;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNzG,OAAA;YAAKoG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrG,OAAA;cAAAqG,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACtCtF,cAAc,CAACN,OAAO,CAACsC,SAAS,CAAC;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACL5F,OAAO,CAAC6D,QAAQ,iBACf1E,OAAA;YAAKoG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrG,OAAA;cAAAqG,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uBACxB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEApG,KAAK,CAACgC,MAAM,GAAG,CAAC,iBACfrC,OAAA;QAAKoG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrG,OAAA;UAAAqG,QAAA,GAAI,iCAAqB,EAAChG,KAAK,CAACgC,MAAM,EAAC,SAAO;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnDzG,OAAA;UAAKoG,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBhG,KAAK,CAAC8G,GAAG,CAAC,CAAC5C,IAAI,EAAE6C,KAAK,kBACrBpH,OAAA;YAEEoG,SAAS,EAAE,aACT7B,IAAI,CAAChB,WAAW,GACZ,WAAW,GACXgB,IAAI,CAACP,KAAK,GACV,UAAU,GACV,MAAM,EACT;YAAAqC,QAAA,gBAEHrG,OAAA;cAAKoG,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB9B,IAAI,CAAChB,WAAW,GAAG,IAAI,GAAGgB,IAAI,CAACP,KAAK,GAAG,OAAO,GAAG;YAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNzG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrG,OAAA;gBAAKoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE9B,IAAI,CAACnB;cAAI;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CzG,OAAA;gBAAKoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvB,CAAC9B,IAAI,CAAChB,WAAW,iBAChBvD,OAAA,CAAAE,SAAA;kBAAAmG,QAAA,gBACErG,OAAA;oBAAMoG,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACxBlF,cAAc,CAACoD,IAAI,CAACR,IAAI;kBAAC;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,EACNlC,IAAI,CAACP,KAAK,iBACThE,OAAA;oBACEoG,SAAS,EAAC,aAAa;oBACvBc,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAACC,IAAI,CAAE;oBACtC8C,KAAK,EAAC,oBAAoB;oBAAAhB,QAAA,EAC3B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,eACDzG,OAAA;oBACEoG,SAAS,EAAC,cAAc;oBACxBc,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACd,IAAI,CAAE;oBAClC8C,KAAK,EAAC,eAAe;oBAAAhB,QAAA,EACtB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACT;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAvCDW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwCP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACrG,EAAA,CA9UQD,GAAG;AAAAmH,EAAA,GAAHnH,GAAG;AAgVZ,eAAeA,GAAG;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}