import React, { useState, useCallback } from "react";
import { unzip } from "fflate";
import "./App.css";

function App() {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const [zipInfo, setZipInfo] = useState(null);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleFileUpload = useCallback(async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith(".zip")) {
      setError("Please select a ZIP file");
      return;
    }

    setLoading(true);
    setError(null);
    setFiles([]);
    setProgress(0);
    setZipInfo({ name: file.name, size: file.size });

    try {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      unzip(uint8Array, (err, unzipped) => {
        if (err) {
          setError(`Error reading ZIP file: ${err.message}`);
          setLoading(false);
          return;
        }

        const fileList = [];
        let totalFiles = 0;
        let totalSize = 0;

        for (const [path, data] of Object.entries(unzipped)) {
          const isDirectory = path.endsWith("/");
          if (!isDirectory) {
            totalFiles++;
            totalSize += data.length;
          }

          fileList.push({
            path,
            name: path.split("/").pop() || path,
            size: isDirectory ? 0 : data.length,
            isDirectory,
            data: isDirectory ? null : data,
          });
        }

        // Sort files: directories first, then files, both alphabetically
        fileList.sort((a, b) => {
          if (a.isDirectory && !b.isDirectory) return -1;
          if (!a.isDirectory && b.isDirectory) return 1;
          return a.path.localeCompare(b.path);
        });

        setFiles(fileList);
        setZipInfo((prev) => ({ ...prev, totalFiles, totalSize }));
        setLoading(false);
        setProgress(100);
      });
    } catch (err) {
      setError(`Error processing file: ${err.message}`);
      setLoading(false);
    }
  }, []);

  const downloadFile = (file) => {
    if (file.isDirectory || !file.data) return;

    const blob = new Blob([file.data]);
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🗜️ BrowserZip Decompressor</h1>
        <p>Upload and explore ZIP files entirely in your browser</p>
      </header>

      <main className="App-main">
        <div className="upload-section">
          <div className="upload-area">
            <input
              type="file"
              accept=".zip"
              onChange={handleFileUpload}
              disabled={loading}
              id="zip-upload"
              className="file-input"
            />
            <label
              htmlFor="zip-upload"
              className={`upload-label ${loading ? "disabled" : ""}`}
            >
              <div className="upload-icon">📁</div>
              <div className="upload-text">
                {loading ? "Processing..." : "Choose ZIP file or drag & drop"}
              </div>
              <div className="upload-hint">Supports files up to 1GB</div>
            </label>
          </div>

          {loading && (
            <div className="progress-section">
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p>Processing ZIP file...</p>
            </div>
          )}

          {error && <div className="error-message">❌ {error}</div>}
        </div>

        {zipInfo && !loading && (
          <div className="zip-info">
            <h3>📦 ZIP File Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <strong>File Name:</strong> {zipInfo.name}
              </div>
              <div className="info-item">
                <strong>Archive Size:</strong> {formatFileSize(zipInfo.size)}
              </div>
              <div className="info-item">
                <strong>Total Files:</strong> {zipInfo.totalFiles}
              </div>
              <div className="info-item">
                <strong>Uncompressed Size:</strong>{" "}
                {formatFileSize(zipInfo.totalSize)}
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="files-section">
            <h3>📋 Files in Archive ({files.length} items)</h3>
            <div className="files-list">
              {files.map((file, index) => (
                <div
                  key={index}
                  className={`file-item ${
                    file.isDirectory ? "directory" : "file"
                  }`}
                >
                  <div className="file-icon">
                    {file.isDirectory ? "📁" : "📄"}
                  </div>
                  <div className="file-details">
                    <div className="file-path">{file.path}</div>
                    <div className="file-meta">
                      {!file.isDirectory && (
                        <>
                          <span className="file-size">
                            {formatFileSize(file.size)}
                          </span>
                          <button
                            className="download-btn"
                            onClick={() => downloadFile(file)}
                            title="Download file"
                          >
                            ⬇️ Download
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
