import React, { useState, useCallback } from "react";
import { unzip } from "fflate";
import "./App.css";

function App() {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const [zipInfo, setZipInfo] = useState(null);
  const [zipStack, setZipStack] = useState([]); // Stack for nested ZIP navigation
  const [currentPath, setCurrentPath] = useState(""); // Current path in nested ZIPs

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const isZipFile = (filename, data) => {
    if (!filename || !data) return false;

    // Check file extension
    const isZipExtension = filename.toLowerCase().endsWith(".zip");

    // Check ZIP file signature (magic bytes)
    const hasZipSignature =
      data.length >= 4 &&
      data[0] === 0x50 &&
      data[1] === 0x4b &&
      (data[2] === 0x03 || data[2] === 0x05 || data[2] === 0x07) &&
      (data[3] === 0x04 || data[3] === 0x06 || data[3] === 0x08);

    return isZipExtension || hasZipSignature;
  };

  const processZipData = useCallback((zipData, zipName, parentPath = "") => {
    return new Promise((resolve, reject) => {
      unzip(zipData, (err, unzipped) => {
        if (err) {
          reject(new Error(`Error reading ZIP file: ${err.message}`));
          return;
        }

        const fileList = [];
        let totalFiles = 0;
        let totalSize = 0;

        for (const [path, data] of Object.entries(unzipped)) {
          const isDirectory = path.endsWith("/");
          const fullPath = parentPath ? `${parentPath}/${path}` : path;

          if (!isDirectory) {
            totalFiles++;
            totalSize += data.length;
          }

          const isNestedZip = !isDirectory && isZipFile(path, data);

          fileList.push({
            path: fullPath,
            originalPath: path,
            name: path.split("/").pop() || path,
            size: isDirectory ? 0 : data.length,
            isDirectory,
            isZip: isNestedZip,
            data: isDirectory ? null : data,
            parentZip: zipName,
          });
        }

        // Sort files: directories first, then files, both alphabetically
        fileList.sort((a, b) => {
          if (a.isDirectory && !b.isDirectory) return -1;
          if (!a.isDirectory && b.isDirectory) return 1;
          return a.path.localeCompare(b.path);
        });

        resolve({ fileList, totalFiles, totalSize });
      });
    });
  }, []);

  const exploreNestedZip = useCallback(
    async (file) => {
      if (!file.isZip || !file.data) return;

      setLoading(true);
      setError(null);

      try {
        const result = await processZipData(file.data, file.name, file.path);

        // Add current state to stack for navigation
        setZipStack((prev) => [
          ...prev,
          {
            files,
            zipInfo,
            path: currentPath,
          },
        ]);

        setFiles(result.fileList);
        setZipInfo({
          name: file.name,
          size: file.size,
          totalFiles: result.totalFiles,
          totalSize: result.totalSize,
          isNested: true,
        });
        setCurrentPath(file.path);
        setLoading(false);
      } catch (err) {
        setError(`Error processing nested ZIP: ${err.message}`);
        setLoading(false);
      }
    },
    [files, zipInfo, currentPath, processZipData]
  );

  const goBack = useCallback(() => {
    if (zipStack.length === 0) return;

    const previousState = zipStack[zipStack.length - 1];
    setFiles(previousState.files);
    setZipInfo(previousState.zipInfo);
    setCurrentPath(previousState.path);
    setZipStack((prev) => prev.slice(0, -1));
  }, [zipStack]);

  const handleFileUpload = useCallback(
    async (event) => {
      const file = event.target.files[0];
      if (!file) return;

      if (!file.name.toLowerCase().endsWith(".zip")) {
        setError("Please select a ZIP file");
        return;
      }

      // Check file size limit (3GB)
      const maxSize = 5 * 1024 * 1024 * 1024; // 5GB in bytes
      if (file.size > maxSize) {
        setError("File size exceeds 5GB limit");
        return;
      }

      setLoading(true);
      setError(null);
      setFiles([]);
      setProgress(0);
      setZipInfo({ name: file.name, size: file.size });

      // Reset navigation state
      setZipStack([]);
      setCurrentPath("");

      try {
        const arrayBuffer = await file.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        const result = await processZipData(uint8Array, file.name);

        setFiles(result.fileList);
        setZipInfo((prev) => ({
          ...prev,
          totalFiles: result.totalFiles,
          totalSize: result.totalSize,
          isNested: false,
        }));
        setLoading(false);
        setProgress(100);
      } catch (err) {
        setError(`Error processing file: ${err.message}`);
        setLoading(false);
      }
    },
    [processZipData]
  );

  const downloadFile = (file) => {
    if (file.isDirectory || !file.data) return;

    const blob = new Blob([file.data]);
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🗜️ BrowserZip Decompressor</h1>
        <p>Upload and explore ZIP files entirely in your browser</p>
      </header>

      <main className="App-main">
        <div className="upload-section">
          <div className="upload-area">
            <input
              type="file"
              accept=".zip"
              onChange={handleFileUpload}
              disabled={loading}
              id="zip-upload"
              className="file-input"
            />
            <label
              htmlFor="zip-upload"
              className={`upload-label ${loading ? "disabled" : ""}`}
            >
              <div className="upload-icon">📁</div>
              <div className="upload-text">
                {loading ? "Processing..." : "Choose ZIP file or drag & drop"}
              </div>
              <div className="upload-hint">
                Supports files up to 5GB with recursive ZIP exploration
              </div>
            </label>
          </div>

          {loading && (
            <div className="progress-section">
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p>Processing ZIP file...</p>
            </div>
          )}

          {error && <div className="error-message">❌ {error}</div>}
        </div>

        {zipInfo && !loading && (
          <div className="zip-info">
            <div className="zip-header">
              <h3>📦 ZIP File Information</h3>
              {zipInfo.isNested && zipStack.length > 0 && (
                <button className="back-btn" onClick={goBack}>
                  ← Back to Parent ZIP
                </button>
              )}
            </div>

            {currentPath && (
              <div className="current-path">
                <strong>Current Path:</strong> {currentPath}
              </div>
            )}

            <div className="info-grid">
              <div className="info-item">
                <strong>File Name:</strong> {zipInfo.name}
              </div>
              <div className="info-item">
                <strong>Archive Size:</strong> {formatFileSize(zipInfo.size)}
              </div>
              <div className="info-item">
                <strong>Total Files:</strong> {zipInfo.totalFiles}
              </div>
              <div className="info-item">
                <strong>Uncompressed Size:</strong>{" "}
                {formatFileSize(zipInfo.totalSize)}
              </div>
              {zipInfo.isNested && (
                <div className="info-item">
                  <strong>Type:</strong> Nested ZIP Archive
                </div>
              )}
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="files-section">
            <h3>📋 Files in Archive ({files.length} items)</h3>
            <div className="files-list">
              {files.map((file, index) => (
                <div
                  key={index}
                  className={`file-item ${
                    file.isDirectory
                      ? "directory"
                      : file.isZip
                      ? "zip-file"
                      : "file"
                  }`}
                >
                  <div className="file-icon">
                    {file.isDirectory ? "📁" : file.isZip ? "[ZIP]" : "📄"}
                  </div>
                  <div className="file-details">
                    <div className="file-path">{file.path}</div>
                    <div className="file-meta">
                      {!file.isDirectory && (
                        <>
                          <span className="file-size">
                            {formatFileSize(file.size)}
                          </span>
                          {file.isZip && (
                            <button
                              className="explore-btn"
                              onClick={() => exploreNestedZip(file)}
                              title="Explore nested ZIP"
                            >
                              🔍 Explore ZIP
                            </button>
                          )}
                          <button
                            className="download-btn"
                            onClick={() => downloadFile(file)}
                            title="Download file"
                          >
                            ⬇️ Download
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
