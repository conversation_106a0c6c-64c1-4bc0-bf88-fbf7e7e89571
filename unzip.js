const fs = require("fs").promises;
const fsSync = require("fs");
const path = require("path");
const { unzip } = require("fflate");

class RecursiveZipProcessor {
  constructor(options = {}) {
    this.maxDepth = options.maxDepth || 10;
    this.maxFileSize = options.maxFileSize || 5 * 1024 * 1024 * 1024; // 5GB
    this.outputDir = options.outputDir || "./extracted";
    this.preserveStructure = options.preserveStructure !== false;
  }

  /**
   * Read large files using streams to bypass Node.js 2GB limit
   */
  async readLargeFile(filePath) {
    return new Promise((resolve, reject) => {
      const chunks = [];
      const stream = fsSync.createReadStream(filePath);

      stream.on("data", (chunk) => {
        chunks.push(chunk);
      });

      stream.on("end", () => {
        const buffer = Buffer.concat(chunks);
        resolve(new Uint8Array(buffer));
      });

      stream.on("error", (error) => {
        reject(error);
      });
    });
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Check if a file is a ZIP file based on extension and magic bytes
   */
  isZipFile(filename, data) {
    if (!filename || !data) return false;

    // Check file extension
    const isZipExtension = filename.toLowerCase().endsWith(".zip");

    // Check ZIP file signature (magic bytes)
    const hasZipSignature =
      data.length >= 4 &&
      data[0] === 0x50 &&
      data[1] === 0x4b &&
      (data[2] === 0x03 || data[2] === 0x05 || data[2] === 0x07) &&
      (data[3] === 0x04 || data[3] === 0x06 || data[3] === 0x08);

    return isZipExtension || hasZipSignature;
  }

  /**
   * Process ZIP data and return file information
   */
  async processZipData(zipData, zipName, parentPath = "", depth = 0) {
    return new Promise((resolve, reject) => {
      if (depth > this.maxDepth) {
        reject(
          new Error(`Maximum recursion depth (${this.maxDepth}) exceeded`)
        );
        return;
      }

      unzip(zipData, (err, unzipped) => {
        if (err) {
          reject(
            new Error(`Error reading ZIP file ${zipName}: ${err.message}`)
          );
          return;
        }

        const fileList = [];
        let totalFiles = 0;
        let totalSize = 0;
        const nestedZips = [];

        for (const [filePath, data] of Object.entries(unzipped)) {
          const isDirectory = filePath.endsWith("/");
          const fullPath = parentPath ? `${parentPath}/${filePath}` : filePath;

          if (!isDirectory) {
            totalFiles++;
            totalSize += data.length;
          }

          const isNestedZip = !isDirectory && this.isZipFile(filePath, data);

          const fileInfo = {
            path: fullPath,
            originalPath: filePath,
            name: path.basename(filePath) || filePath,
            size: isDirectory ? 0 : data.length,
            isDirectory,
            isZip: isNestedZip,
            data: isDirectory ? null : data,
            parentZip: zipName,
            depth,
          };

          fileList.push(fileInfo);

          // Collect nested ZIP files for recursive processing
          if (isNestedZip) {
            nestedZips.push(fileInfo);
          }
        }

        // Sort files: directories first, then files, both alphabetically
        fileList.sort((a, b) => {
          if (a.isDirectory && !b.isDirectory) return -1;
          if (!a.isDirectory && b.isDirectory) return 1;
          return a.path.localeCompare(b.path);
        });

        resolve({
          fileList,
          totalFiles,
          totalSize,
          nestedZips,
          depth,
        });
      });
    });
  }

  /**
   * Recursively process all nested ZIP files
   */
  async processNestedZips(nestedZips, allFiles = [], depth = 0) {
    for (const zipFile of nestedZips) {
      try {
        console.log(
          `Processing nested ZIP: ${zipFile.path} (depth: ${depth + 1})`
        );

        const result = await this.processZipData(
          zipFile.data,
          zipFile.name,
          zipFile.path.replace(/\.zip$/i, ""),
          depth + 1
        );

        // Add all files from nested ZIP
        allFiles.push(...result.fileList);

        // Recursively process any nested ZIPs found in this ZIP
        if (result.nestedZips.length > 0) {
          await this.processNestedZips(result.nestedZips, allFiles, depth + 1);
        }
      } catch (error) {
        console.error(
          `Error processing nested ZIP ${zipFile.path}:`,
          error.message
        );
      }
    }

    return allFiles;
  }

  /**
   * Extract files to filesystem
   */
  async extractFiles(files, baseOutputDir = this.outputDir) {
    await fs.mkdir(baseOutputDir, { recursive: true });

    for (const file of files) {
      if (file.isDirectory) continue;

      const outputPath = this.preserveStructure
        ? path.join(baseOutputDir, file.path)
        : path.join(baseOutputDir, file.name);

      // Create directory structure
      await fs.mkdir(path.dirname(outputPath), { recursive: true });

      // Write file
      await fs.writeFile(outputPath, file.data);
      console.log(
        `Extracted: ${outputPath} (${this.formatFileSize(file.size)})`
      );
    }
  }

  /**
   * Main function to process a ZIP file recursively
   */
  async processZipFile(zipFilePath, options = {}) {
    const startTime = Date.now();
    console.log(`Starting recursive ZIP processing: ${zipFilePath}`);

    try {
      // Check file size first
      const stats = await fs.stat(zipFilePath);
      if (stats.size > this.maxFileSize) {
        throw new Error(
          `File size (${this.formatFileSize(
            stats.size
          )}) exceeds maximum allowed size (${this.formatFileSize(
            this.maxFileSize
          )})`
        );
      }

      console.log(`Reading file: ${this.formatFileSize(stats.size)}`);

      // Read the main ZIP file using streaming for large files
      const zipData = await this.readLargeFile(zipFilePath);

      console.log(
        `File loaded into memory: ${this.formatFileSize(zipData.length)}`
      );

      // Log memory usage for large files
      const memUsage = process.memoryUsage();
      console.log(
        `Memory usage: ${this.formatFileSize(
          memUsage.heapUsed
        )} / ${this.formatFileSize(memUsage.heapTotal)}`
      );

      // Process the main ZIP file
      const mainResult = await this.processZipData(
        zipData,
        path.basename(zipFilePath)
      );

      console.log(
        `Main ZIP processed: ${
          mainResult.totalFiles
        } files, ${this.formatFileSize(mainResult.totalSize)} total size`
      );

      let allFiles = [...mainResult.fileList];

      // Process nested ZIPs recursively
      if (mainResult.nestedZips.length > 0) {
        console.log(
          `Found ${mainResult.nestedZips.length} nested ZIP files. Processing recursively...`
        );
        const nestedFiles = await this.processNestedZips(
          mainResult.nestedZips,
          [],
          0
        );
        allFiles.push(...nestedFiles);
      }

      // Generate summary
      const summary = this.generateSummary(allFiles);

      const processingTime = Date.now() - startTime;
      console.log(`\nProcessing completed in ${processingTime}ms`);
      console.log(summary);

      // Extract files if requested
      if (options.extract) {
        console.log("\nExtracting files...");
        await this.extractFiles(allFiles, options.outputDir || this.outputDir);
        console.log(
          `Files extracted to: ${options.outputDir || this.outputDir}`
        );
      }

      return {
        files: allFiles,
        summary,
        processingTime,
      };
    } catch (error) {
      console.error("Error processing ZIP file:", error.message);
      throw error;
    }
  }

  /**
   * Generate processing summary
   */
  generateSummary(files) {
    const totalFiles = files.filter((f) => !f.isDirectory).length;
    const totalDirectories = files.filter((f) => f.isDirectory).length;
    const totalZips = files.filter((f) => f.isZip).length;
    const totalSize = files.reduce((sum, f) => sum + (f.size || 0), 0);
    const maxDepth = Math.max(...files.map((f) => f.depth || 0));

    const summary = {
      totalFiles,
      totalDirectories,
      totalZips,
      totalSize: this.formatFileSize(totalSize),
      maxDepth,
      filesByDepth: {},
    };

    // Group files by depth
    files.forEach((file) => {
      const depth = file.depth || 0;
      if (!summary.filesByDepth[depth]) {
        summary.filesByDepth[depth] = 0;
      }
      if (!file.isDirectory) {
        summary.filesByDepth[depth]++;
      }
    });

    return summary;
  }

  /**
   * List files without extracting
   */
  async listFiles(zipFilePath) {
    const result = await this.processZipFile(zipFilePath, { extract: false });

    console.log("\n=== FILE LISTING ===");
    result.files.forEach((file) => {
      const indent = "  ".repeat(file.depth || 0);
      const icon = file.isDirectory ? "📁" : file.isZip ? "[ZIP]" : "📄";
      const size = file.isDirectory
        ? ""
        : ` (${this.formatFileSize(file.size)})`;
      console.log(`${indent}${icon} ${file.path}${size}`);
    });

    return result.files;
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log("Usage: node unzip.js <zip-file> [options]");
    console.log("Options:");
    console.log("  --extract, -e          Extract files");
    console.log(
      "  --output, -o <dir>     Output directory (default: ./extracted)"
    );
    console.log(
      "  --max-depth <n>        Maximum recursion depth (default: 10)"
    );
    console.log("  --max-size <bytes>     Maximum file size (default: 5GB)");
    console.log("  --list, -l             List files only (no extraction)");
    process.exit(1);
  }

  const zipFile = args[0];
  const options = {
    extract: args.includes("--extract") || args.includes("-e"),
    list: args.includes("--list") || args.includes("-l"),
  };

  // Parse output directory
  const outputIndex = args.findIndex(
    (arg) => arg === "--output" || arg === "-o"
  );
  if (outputIndex !== -1 && args[outputIndex + 1]) {
    options.outputDir = args[outputIndex + 1];
  }

  // Parse max depth
  const depthIndex = args.findIndex((arg) => arg === "--max-depth");
  if (depthIndex !== -1 && args[depthIndex + 1]) {
    options.maxDepth = parseInt(args[depthIndex + 1]);
  }

  const processor = new RecursiveZipProcessor(options);

  if (options.list) {
    processor.listFiles(zipFile).catch(console.error);
  } else {
    processor.processZipFile(zipFile, options).catch(console.error);
  }
}

module.exports = RecursiveZipProcessor;
