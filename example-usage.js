const RecursiveZipProcessor = require('./unzip');
const path = require('path');

// Example 1: Basic usage - list files only
async function example1() {
  console.log('=== Example 1: List files in ZIP ===');
  
  const processor = new RecursiveZipProcessor({
    maxDepth: 5,
    maxFileSize: 5 * 1024 * 1024 * 1024 // 5GB
  });

  try {
    // Replace 'example.zip' with your actual ZIP file path
    const files = await processor.listFiles('example.zip');
    console.log(`Found ${files.length} total items`);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Example 2: Extract all files with custom output directory
async function example2() {
  console.log('\n=== Example 2: Extract files ===');
  
  const processor = new RecursiveZipProcessor({
    outputDir: './extracted-files',
    preserveStructure: true
  });

  try {
    const result = await processor.processZipFile('example.zip', {
      extract: true,
      outputDir: './my-extracted-files'
    });
    
    console.log('Extraction Summary:');
    console.log(`- Total files: ${result.summary.totalFiles}`);
    console.log(`- Total directories: ${result.summary.totalDirectories}`);
    console.log(`- Nested ZIPs found: ${result.summary.totalZips}`);
    console.log(`- Total size: ${result.summary.totalSize}`);
    console.log(`- Max depth: ${result.summary.maxDepth}`);
    console.log(`- Processing time: ${result.processingTime}ms`);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Example 3: Process with custom options
async function example3() {
  console.log('\n=== Example 3: Custom processing ===');
  
  const processor = new RecursiveZipProcessor({
    maxDepth: 3,
    maxFileSize: 1024 * 1024 * 1024, // 1GB
    outputDir: './custom-output',
    preserveStructure: false // Flatten directory structure
  });

  try {
    const result = await processor.processZipFile('example.zip', {
      extract: true
    });
    
    // Filter and display only ZIP files found
    const zipFiles = result.files.filter(f => f.isZip);
    console.log('\nNested ZIP files found:');
    zipFiles.forEach(zip => {
      console.log(`- ${zip.path} (${processor.formatFileSize(zip.size)}) at depth ${zip.depth}`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Example 4: Programmatic file processing
async function example4() {
  console.log('\n=== Example 4: Programmatic processing ===');
  
  const processor = new RecursiveZipProcessor();

  try {
    const result = await processor.processZipFile('example.zip', { extract: false });
    
    // Process files programmatically
    const imageFiles = result.files.filter(f => 
      !f.isDirectory && /\.(jpg|jpeg|png|gif|bmp)$/i.test(f.name)
    );
    
    const textFiles = result.files.filter(f => 
      !f.isDirectory && /\.(txt|md|json|xml|csv)$/i.test(f.name)
    );
    
    console.log(`Found ${imageFiles.length} image files:`);
    imageFiles.forEach(img => console.log(`  - ${img.path}`));
    
    console.log(`Found ${textFiles.length} text files:`);
    textFiles.forEach(txt => console.log(`  - ${txt.path}`));
    
    // Save specific files
    const fs = require('fs').promises;
    for (const textFile of textFiles.slice(0, 3)) { // Save first 3 text files
      const outputPath = `./text-files/${textFile.name}`;
      await fs.mkdir('./text-files', { recursive: true });
      await fs.writeFile(outputPath, textFile.data);
      console.log(`Saved: ${outputPath}`);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run examples
async function runExamples() {
  console.log('Recursive ZIP Processor Examples\n');
  
  // Uncomment the examples you want to run
  // await example1();
  // await example2();
  // await example3();
  // await example4();
  
  console.log('\nTo run these examples:');
  console.log('1. Install dependencies: npm install fflate');
  console.log('2. Place a ZIP file named "example.zip" in this directory');
  console.log('3. Uncomment the example functions above');
  console.log('4. Run: node example-usage.js');
}

if (require.main === module) {
  runExamples().catch(console.error);
}

module.exports = {
  example1,
  example2,
  example3,
  example4
};
