@echo off
setlocal enabledelayedexpansion

REM Recursive ZIP Processor using 7zip for Windows
REM Handles nested ZIP files recursively

set "ZIP_FILE=Manual.zip"
set "OUTPUT_DIR=extracted"
set "MAX_DEPTH=10"
set "LIST_ONLY=false"
set "VERBOSE=false"
set "CURRENT_DEPTH=0"

REM Parse command line arguments
:parse_args
if "%~1"=="" goto start_processing
if "%~1"=="-o" (
    set "OUTPUT_DIR=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="--output" (
    set "OUTPUT_DIR=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="-d" (
    set "MAX_DEPTH=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="--max-depth" (
    set "MAX_DEPTH=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="-l" (
    set "LIST_ONLY=true"
    shift
    goto parse_args
)
if "%~1"=="--list" (
    set "LIST_ONLY=true"
    shift
    goto parse_args
)
if "%~1"=="-v" (
    set "VERBOSE=true"
    shift
    goto parse_args
)
if "%~1"=="--verbose" (
    set "VERBOSE=true"
    shift
    goto parse_args
)
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help
set "ZIP_FILE=%~1"
shift
goto parse_args

:show_help
echo Recursive ZIP Processor using 7zip for Windows
echo.
echo Usage: %0 [zip-file] [options]
echo.
echo Options:
echo   -o, --output DIR      Output directory (default: extracted)
echo   -d, --max-depth N     Maximum recursion depth (default: 10)
echo   -l, --list           List files only, don't extract
echo   -v, --verbose        Verbose output
echo   -h, --help           Show this help
echo.
echo Examples:
echo   %0                                    # Process Manual.zip with defaults
echo   %0 Manual.zip -o manuals -d 5        # Extract to manuals folder, max depth 5
echo   %0 Manual.zip --list --verbose       # List all files with details
echo.
echo Requirements:
echo   - 7-Zip must be installed and in PATH
echo   - Or 7z.exe must be in the same directory as this script
goto end

:start_processing
REM Check if 7zip is available
where 7z >nul 2>&1
if %errorlevel% neq 0 (
    if exist "7z.exe" (
        set "SEVENZIP=7z.exe"
    ) else (
        echo Error: 7-Zip not found. Please install 7-Zip and add it to PATH
        echo Or place 7z.exe in the same directory as this script
        echo Download from: https://www.7-zip.org/
        goto end
    )
) else (
    set "SEVENZIP=7z"
)

REM Check if ZIP file exists
if not exist "%ZIP_FILE%" (
    echo Error: File '%ZIP_FILE%' not found
    goto end
)

REM Print configuration
echo === Recursive ZIP Processor ===
echo File: %ZIP_FILE%
echo Output directory: %OUTPUT_DIR%
echo Maximum depth: %MAX_DEPTH%
if "%LIST_ONLY%"=="true" (
    echo Mode: List only
) else (
    echo Mode: Extract
)
echo Verbose: %VERBOSE%
echo.

REM Get file size
for %%A in ("%ZIP_FILE%") do set "FILE_SIZE=%%~zA"
echo File size: %FILE_SIZE% bytes
echo.

REM Test if it's a valid ZIP file
%SEVENZIP% t "%ZIP_FILE%" >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: '%ZIP_FILE%' does not appear to be a valid ZIP file
    goto end
)

echo Starting processing...
echo.

REM Record start time
set "START_TIME=%time%"

REM Process the file
if "%LIST_ONLY%"=="true" (
    echo === FILE LISTING ===
    call :list_archive "%ZIP_FILE%" 0 ""
) else (
    echo === EXTRACTION ===
    call :extract_archive "%ZIP_FILE%" "%OUTPUT_DIR%" 0
    echo Files extracted to: %OUTPUT_DIR%
)

REM Calculate and display end time
set "END_TIME=%time%"
echo.
echo === Processing Completed ===
echo Start time: %START_TIME%
echo End time: %END_TIME%

goto end

:list_archive
set "archive=%~1"
set "depth=%~2"
set "prefix=%~3"

if %depth% gtr %MAX_DEPTH% (
    echo Warning: Maximum depth (%MAX_DEPTH%) reached for %archive%
    goto :eof
)

REM Create indentation
set "indent="
for /l %%i in (1,1,%depth%) do set "indent=  !indent!"

if "%VERBOSE%"=="true" (
    echo !indent!Processing: %~n1%~x1 ^(depth: %depth%^)
)

REM Create temp directory
set "temp_dir=temp_list_%depth%_%random%"
mkdir "%temp_dir%" 2>nul

REM Extract to temp directory
%SEVENZIP% x "%archive%" -o"%temp_dir%" >nul 2>&1
if %errorlevel% neq 0 (
    echo !indent!Error: Cannot extract %archive%
    rmdir /s /q "%temp_dir%" 2>nul
    goto :eof
)

REM List files and find nested ZIPs
for /r "%temp_dir%" %%f in (*) do (
    set "file=%%f"
    set "rel_path=!file:%temp_dir%\=!"
    set "full_path=%prefix%!rel_path!"
    
    REM Get file size
    for %%A in ("%%f") do set "size=%%~zA"
    
    REM Check if it's a ZIP file
    echo "%%f" | findstr /i "\.zip$" >nul
    if !errorlevel! equ 0 (
        echo !indent!🗜️ !full_path! ^(!size! bytes^) [ZIP]
        REM Process nested ZIP if within depth limit
        if %depth% lss %MAX_DEPTH% (
            call :list_archive "%%f" %depth%+1 "!full_path:~0,-4!/"
        )
    ) else (
        echo !indent!📄 !full_path! ^(!size! bytes^)
    )
)

REM Clean up temp directory
rmdir /s /q "%temp_dir%" 2>nul
goto :eof

:extract_archive
set "archive=%~1"
set "output=%~2"
set "depth=%~3"

if %depth% gtr %MAX_DEPTH% (
    echo Warning: Maximum depth (%MAX_DEPTH%) reached for %archive%
    goto :eof
)

REM Create indentation
set "indent="
for /l %%i in (1,1,%depth%) do set "indent=  !indent!"

if "%VERBOSE%"=="true" (
    echo !indent!Extracting: %~n1%~x1 ^(depth: %depth%^)
)

REM Create output directory
mkdir "%output%" 2>nul

REM Extract the archive
%SEVENZIP% x "%archive%" -o"%output%" >nul 2>&1
if %errorlevel% neq 0 (
    echo !indent!Error: Cannot extract %archive%
    goto :eof
)

REM Find and process nested ZIP files
for /r "%output%" %%f in (*.zip *.ZIP) do (
    if exist "%%f" (
        set "zip_path=%%f"
        set "zip_name=%%~nf"
        set "zip_dir=%%~dpf!zip_name!_extracted"
        
        if "%VERBOSE%"=="true" (
            echo !indent!Found nested ZIP: %%~nxf
        )
        
        REM Extract nested ZIP if within depth limit
        if %depth% lss %MAX_DEPTH% (
            call :extract_archive "%%f" "!zip_dir!" %depth%+1
        )
    )
)

goto :eof

:end
pause
