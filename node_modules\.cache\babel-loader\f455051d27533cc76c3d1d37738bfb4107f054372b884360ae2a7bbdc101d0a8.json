{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\BrowserZip Decompressor and list\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from \"react\";\nimport { unzip } from \"fflate\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [files, setFiles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [progress, setProgress] = useState(0);\n  const [zipInfo, setZipInfo] = useState(null);\n  const formatFileSize = bytes => {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  };\n  const handleFileUpload = useCallback(async event => {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (!file.name.toLowerCase().endsWith(\".zip\")) {\n      setError(\"Please select a ZIP file\");\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setFiles([]);\n    setProgress(0);\n    setZipInfo({\n      name: file.name,\n      size: file.size\n    });\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const uint8Array = new Uint8Array(arrayBuffer);\n      unzip(uint8Array, (err, unzipped) => {\n        if (err) {\n          setError(`Error reading ZIP file: ${err.message}`);\n          setLoading(false);\n          return;\n        }\n        const fileList = [];\n        let totalFiles = 0;\n        let totalSize = 0;\n        for (const [path, data] of Object.entries(unzipped)) {\n          const isDirectory = path.endsWith(\"/\");\n          if (!isDirectory) {\n            totalFiles++;\n            totalSize += data.length;\n          }\n          fileList.push({\n            path,\n            name: path.split(\"/\").pop() || path,\n            size: isDirectory ? 0 : data.length,\n            isDirectory,\n            data: isDirectory ? null : data\n          });\n        }\n\n        // Sort files: directories first, then files, both alphabetically\n        fileList.sort((a, b) => {\n          if (a.isDirectory && !b.isDirectory) return -1;\n          if (!a.isDirectory && b.isDirectory) return 1;\n          return a.path.localeCompare(b.path);\n        });\n        setFiles(fileList);\n        setZipInfo(prev => ({\n          ...prev,\n          totalFiles,\n          totalSize\n        }));\n        setLoading(false);\n        setProgress(100);\n      });\n    } catch (err) {\n      setError(`Error processing file: ${err.message}`);\n      setLoading(false);\n    }\n  }, []);\n  const downloadFile = file => {\n    if (file.isDirectory || !file.data) return;\n    const blob = new Blob([file.data]);\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = file.name;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDDDC\\uFE0F BrowserZip Decompressor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload and explore ZIP files entirely in your browser\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"App-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-area\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            accept: \".zip\",\n            onChange: handleFileUpload,\n            disabled: loading,\n            id: \"zip-upload\",\n            className: \"file-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"zip-upload\",\n            className: `upload-label ${loading ? \"disabled\" : \"\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-icon\",\n              children: \"\\uD83D\\uDCC1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-text\",\n              children: loading ? \"Processing...\" : \"Choose ZIP file or drag & drop\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-hint\",\n              children: \"Supports files up to 1GB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Processing ZIP file...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [\"\\u274C \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), zipInfo && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"zip-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDCE6 ZIP File Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"File Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), \" \", zipInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Archive Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), \" \", formatFileSize(zipInfo.size)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total Files:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), \" \", zipInfo.totalFiles]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Uncompressed Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), \" \", formatFileSize(zipInfo.totalSize)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"files-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"\\uD83D\\uDCCB Files in Archive (\", files.length, \" items)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"files-list\",\n          children: files.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `file-item ${file.isDirectory ? \"directory\" : \"file\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-icon\",\n              children: file.isDirectory ? \"📁\" : \"📄\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-path\",\n                children: file.path\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-meta\",\n                children: !file.isDirectory && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-size\",\n                    children: formatFileSize(file.size)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"download-btn\",\n                    onClick: () => downloadFile(file),\n                    title: \"Download file\",\n                    children: \"\\u2B07\\uFE0F Download\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"K7QGWYboIGK7xeU70Ado+qLpUf8=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "unzip", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "files", "setFiles", "loading", "setLoading", "error", "setError", "progress", "setProgress", "zipInfo", "setZipInfo", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "handleFileUpload", "event", "file", "target", "name", "toLowerCase", "endsWith", "size", "arrayBuffer", "uint8Array", "Uint8Array", "err", "unzipped", "message", "fileList", "totalFiles", "totalSize", "path", "data", "Object", "entries", "isDirectory", "length", "push", "split", "pop", "sort", "a", "b", "localeCompare", "prev", "downloadFile", "blob", "Blob", "url", "URL", "createObjectURL", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "accept", "onChange", "disabled", "id", "htmlFor", "style", "width", "map", "index", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/BrowserZip Decompressor and list/src/App.js"], "sourcesContent": ["import React, { useState, useCallback } from \"react\";\nimport { unzip } from \"fflate\";\nimport \"./App.css\";\n\nfunction App() {\n  const [files, setFiles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [progress, setProgress] = useState(0);\n  const [zipInfo, setZipInfo] = useState(null);\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  };\n\n  const handleFileUpload = useCallback(async (event) => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    if (!file.name.toLowerCase().endsWith(\".zip\")) {\n      setError(\"Please select a ZIP file\");\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setFiles([]);\n    setProgress(0);\n    setZipInfo({ name: file.name, size: file.size });\n\n    try {\n      const arrayBuffer = await file.arrayBuffer();\n      const uint8Array = new Uint8Array(arrayBuffer);\n\n      unzip(uint8Array, (err, unzipped) => {\n        if (err) {\n          setError(`Error reading ZIP file: ${err.message}`);\n          setLoading(false);\n          return;\n        }\n\n        const fileList = [];\n        let totalFiles = 0;\n        let totalSize = 0;\n\n        for (const [path, data] of Object.entries(unzipped)) {\n          const isDirectory = path.endsWith(\"/\");\n          if (!isDirectory) {\n            totalFiles++;\n            totalSize += data.length;\n          }\n\n          fileList.push({\n            path,\n            name: path.split(\"/\").pop() || path,\n            size: isDirectory ? 0 : data.length,\n            isDirectory,\n            data: isDirectory ? null : data,\n          });\n        }\n\n        // Sort files: directories first, then files, both alphabetically\n        fileList.sort((a, b) => {\n          if (a.isDirectory && !b.isDirectory) return -1;\n          if (!a.isDirectory && b.isDirectory) return 1;\n          return a.path.localeCompare(b.path);\n        });\n\n        setFiles(fileList);\n        setZipInfo((prev) => ({ ...prev, totalFiles, totalSize }));\n        setLoading(false);\n        setProgress(100);\n      });\n    } catch (err) {\n      setError(`Error processing file: ${err.message}`);\n      setLoading(false);\n    }\n  }, []);\n\n  const downloadFile = (file) => {\n    if (file.isDirectory || !file.data) return;\n\n    const blob = new Blob([file.data]);\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = file.name;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>🗜️ BrowserZip Decompressor</h1>\n        <p>Upload and explore ZIP files entirely in your browser</p>\n      </header>\n\n      <main className=\"App-main\">\n        <div className=\"upload-section\">\n          <div className=\"upload-area\">\n            <input\n              type=\"file\"\n              accept=\".zip\"\n              onChange={handleFileUpload}\n              disabled={loading}\n              id=\"zip-upload\"\n              className=\"file-input\"\n            />\n            <label\n              htmlFor=\"zip-upload\"\n              className={`upload-label ${loading ? \"disabled\" : \"\"}`}\n            >\n              <div className=\"upload-icon\">📁</div>\n              <div className=\"upload-text\">\n                {loading ? \"Processing...\" : \"Choose ZIP file or drag & drop\"}\n              </div>\n              <div className=\"upload-hint\">Supports files up to 1GB</div>\n            </label>\n          </div>\n\n          {loading && (\n            <div className=\"progress-section\">\n              <div className=\"progress-bar\">\n                <div\n                  className=\"progress-fill\"\n                  style={{ width: `${progress}%` }}\n                ></div>\n              </div>\n              <p>Processing ZIP file...</p>\n            </div>\n          )}\n\n          {error && <div className=\"error-message\">❌ {error}</div>}\n        </div>\n\n        {zipInfo && !loading && (\n          <div className=\"zip-info\">\n            <h3>📦 ZIP File Information</h3>\n            <div className=\"info-grid\">\n              <div className=\"info-item\">\n                <strong>File Name:</strong> {zipInfo.name}\n              </div>\n              <div className=\"info-item\">\n                <strong>Archive Size:</strong> {formatFileSize(zipInfo.size)}\n              </div>\n              <div className=\"info-item\">\n                <strong>Total Files:</strong> {zipInfo.totalFiles}\n              </div>\n              <div className=\"info-item\">\n                <strong>Uncompressed Size:</strong>{\" \"}\n                {formatFileSize(zipInfo.totalSize)}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {files.length > 0 && (\n          <div className=\"files-section\">\n            <h3>📋 Files in Archive ({files.length} items)</h3>\n            <div className=\"files-list\">\n              {files.map((file, index) => (\n                <div\n                  key={index}\n                  className={`file-item ${\n                    file.isDirectory ? \"directory\" : \"file\"\n                  }`}\n                >\n                  <div className=\"file-icon\">\n                    {file.isDirectory ? \"📁\" : \"📄\"}\n                  </div>\n                  <div className=\"file-details\">\n                    <div className=\"file-path\">{file.path}</div>\n                    <div className=\"file-meta\">\n                      {!file.isDirectory && (\n                        <>\n                          <span className=\"file-size\">\n                            {formatFileSize(file.size)}\n                          </span>\n                          <button\n                            className=\"download-btn\"\n                            onClick={() => downloadFile(file)}\n                            title=\"Download file\"\n                          >\n                            ⬇️ Download\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAASC,KAAK,QAAQ,QAAQ;AAC9B,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMmB,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,gBAAgB,GAAG7B,WAAW,CAAC,MAAO8B,KAAK,IAAK;IACpD,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACxB,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACuB,IAAI,EAAE;IAEX,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7CtB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdJ,QAAQ,CAAC,EAAE,CAAC;IACZM,WAAW,CAAC,CAAC,CAAC;IACdE,UAAU,CAAC;MAAEgB,IAAI,EAAEF,IAAI,CAACE,IAAI;MAAEG,IAAI,EAAEL,IAAI,CAACK;IAAK,CAAC,CAAC;IAEhD,IAAI;MACF,MAAMC,WAAW,GAAG,MAAMN,IAAI,CAACM,WAAW,CAAC,CAAC;MAC5C,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACF,WAAW,CAAC;MAE9CpC,KAAK,CAACqC,UAAU,EAAE,CAACE,GAAG,EAAEC,QAAQ,KAAK;QACnC,IAAID,GAAG,EAAE;UACP3B,QAAQ,CAAC,2BAA2B2B,GAAG,CAACE,OAAO,EAAE,CAAC;UAClD/B,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAMgC,QAAQ,GAAG,EAAE;QACnB,IAAIC,UAAU,GAAG,CAAC;QAClB,IAAIC,SAAS,GAAG,CAAC;QAEjB,KAAK,MAAM,CAACC,IAAI,EAAEC,IAAI,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;UACnD,MAAMS,WAAW,GAAGJ,IAAI,CAACX,QAAQ,CAAC,GAAG,CAAC;UACtC,IAAI,CAACe,WAAW,EAAE;YAChBN,UAAU,EAAE;YACZC,SAAS,IAAIE,IAAI,CAACI,MAAM;UAC1B;UAEAR,QAAQ,CAACS,IAAI,CAAC;YACZN,IAAI;YACJb,IAAI,EAAEa,IAAI,CAACO,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,IAAIR,IAAI;YACnCV,IAAI,EAAEc,WAAW,GAAG,CAAC,GAAGH,IAAI,CAACI,MAAM;YACnCD,WAAW;YACXH,IAAI,EAAEG,WAAW,GAAG,IAAI,GAAGH;UAC7B,CAAC,CAAC;QACJ;;QAEA;QACAJ,QAAQ,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACtB,IAAID,CAAC,CAACN,WAAW,IAAI,CAACO,CAAC,CAACP,WAAW,EAAE,OAAO,CAAC,CAAC;UAC9C,IAAI,CAACM,CAAC,CAACN,WAAW,IAAIO,CAAC,CAACP,WAAW,EAAE,OAAO,CAAC;UAC7C,OAAOM,CAAC,CAACV,IAAI,CAACY,aAAa,CAACD,CAAC,CAACX,IAAI,CAAC;QACrC,CAAC,CAAC;QAEFrC,QAAQ,CAACkC,QAAQ,CAAC;QAClB1B,UAAU,CAAE0C,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAEf,UAAU;UAAEC;QAAU,CAAC,CAAC,CAAC;QAC1DlC,UAAU,CAAC,KAAK,CAAC;QACjBI,WAAW,CAAC,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACZ3B,QAAQ,CAAC,0BAA0B2B,GAAG,CAACE,OAAO,EAAE,CAAC;MACjD/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiD,YAAY,GAAI7B,IAAI,IAAK;IAC7B,IAAIA,IAAI,CAACmB,WAAW,IAAI,CAACnB,IAAI,CAACgB,IAAI,EAAE;IAEpC,MAAMc,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC/B,IAAI,CAACgB,IAAI,CAAC,CAAC;IAClC,MAAMgB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAML,CAAC,GAAGU,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCX,CAAC,CAACY,IAAI,GAAGL,GAAG;IACZP,CAAC,CAACa,QAAQ,GAAGtC,IAAI,CAACE,IAAI;IACtBiC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACf,CAAC,CAAC;IAC5BA,CAAC,CAACgB,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACjB,CAAC,CAAC;IAC5BQ,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,oBACE5D,OAAA;IAAKwE,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBzE,OAAA;MAAQwE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5BzE,OAAA;QAAAyE,QAAA,EAAI;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpC7E,OAAA;QAAAyE,QAAA,EAAG;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAET7E,OAAA;MAAMwE,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACxBzE,OAAA;QAAKwE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzE,OAAA;UAAKwE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BzE,OAAA;YACE8E,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,MAAM;YACbC,QAAQ,EAAEtD,gBAAiB;YAC3BuD,QAAQ,EAAE1E,OAAQ;YAClB2E,EAAE,EAAC,YAAY;YACfV,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACF7E,OAAA;YACEmF,OAAO,EAAC,YAAY;YACpBX,SAAS,EAAE,gBAAgBjE,OAAO,GAAG,UAAU,GAAG,EAAE,EAAG;YAAAkE,QAAA,gBAEvDzE,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrC7E,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,EACzBlE,OAAO,GAAG,eAAe,GAAG;YAAgC;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN7E,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAELtE,OAAO,iBACNP,OAAA;UAAKwE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BzE,OAAA;YAAKwE,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BzE,OAAA;cACEwE,SAAS,EAAC,eAAe;cACzBY,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAG1E,QAAQ;cAAI;YAAE;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7E,OAAA;YAAAyE,QAAA,EAAG;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACN,EAEApE,KAAK,iBAAIT,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,SAAE,EAAChE,KAAK;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,EAELhE,OAAO,IAAI,CAACN,OAAO,iBAClBP,OAAA;QAAKwE,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBzE,OAAA;UAAAyE,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChC7E,OAAA;UAAKwE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzE,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzE,OAAA;cAAAyE,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAChE,OAAO,CAACiB,IAAI;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN7E,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzE,OAAA;cAAAyE,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9D,cAAc,CAACF,OAAO,CAACoB,IAAI,CAAC;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN7E,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzE,OAAA;cAAAyE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAChE,OAAO,CAAC4B,UAAU;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN7E,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzE,OAAA;cAAAyE,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACtC9D,cAAc,CAACF,OAAO,CAAC6B,SAAS,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAxE,KAAK,CAAC2C,MAAM,GAAG,CAAC,iBACfhD,OAAA;QAAKwE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzE,OAAA;UAAAyE,QAAA,GAAI,iCAAqB,EAACpE,KAAK,CAAC2C,MAAM,EAAC,SAAO;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnD7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBpE,KAAK,CAACiF,GAAG,CAAC,CAAC1D,IAAI,EAAE2D,KAAK,kBACrBvF,OAAA;YAEEwE,SAAS,EAAE,aACT5C,IAAI,CAACmB,WAAW,GAAG,WAAW,GAAG,MAAM,EACtC;YAAA0B,QAAA,gBAEHzE,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB7C,IAAI,CAACmB,WAAW,GAAG,IAAI,GAAG;YAAI;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACN7E,OAAA;cAAKwE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BzE,OAAA;gBAAKwE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE7C,IAAI,CAACe;cAAI;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5C7E,OAAA;gBAAKwE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvB,CAAC7C,IAAI,CAACmB,WAAW,iBAChB/C,OAAA,CAAAE,SAAA;kBAAAuE,QAAA,gBACEzE,OAAA;oBAAMwE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACxB1D,cAAc,CAACa,IAAI,CAACK,IAAI;kBAAC;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACP7E,OAAA;oBACEwE,SAAS,EAAC,cAAc;oBACxBgB,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC7B,IAAI,CAAE;oBAClC6D,KAAK,EAAC,eAAe;oBAAAhB,QAAA,EACtB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACT;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA1BDU,KAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACzE,EAAA,CAxMQD,GAAG;AAAAuF,EAAA,GAAHvF,GAAG;AA0MZ,eAAeA,GAAG;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}