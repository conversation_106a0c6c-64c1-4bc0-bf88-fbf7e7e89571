# @babel/eslint-parser

> allows you to lint all valid Babel code with the fantastic ESLint

See our website [@babel/eslint-parser](https://babeljs.io/docs/babel-eslint-parser) for more information or the [issues](https://github.com/babel/babel/issues?q=is%3Aissue%20state%3Aopen%20label%3A%22area%3A%20eslint%22) associated with this package.

## Install

Using npm:

```sh
npm install --save-dev @babel/eslint-parser
```

or using yarn:

```sh
yarn add @babel/eslint-parser --dev
```
