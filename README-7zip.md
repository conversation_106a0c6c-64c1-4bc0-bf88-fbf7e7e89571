# Recursive ZIP Processing with 7zip

This solution uses 7zip to recursively process ZIP files, including nested ZIP archives. It's more efficient than the Node.js approach for very large files and doesn't have the 2GB memory limitations.

## Files Created

- **`recursive-unzip.sh`** - Main bash script for Unix/Linux/macOS/Git Bash
- **`recursive-unzip.bat`** - Windows batch script
- **`test-manual.sh`** - Interactive test script for your Manual.zip file
- **`README-7zip.md`** - This documentation

## Quick Start

### For your Manual.zip file:

**Linux/macOS/Git Bash:**
```bash
# Make executable (first time only)
chmod +x recursive-unzip.sh test-manual.sh

# Interactive test
./test-manual.sh

# Or directly:
./recursive-unzip.sh Manual.zip --list          # List contents
./recursive-unzip.sh Manual.zip -o ./manuals    # Extract all
```

**Windows Command Prompt:**
```cmd
recursive-unzip.bat Manual.zip --list           # List contents
recursive-unzip.bat Manual.zip -o manuals       # Extract all
```

## Features

- ✅ **No file size limits** - Handles files larger than 2GB
- ✅ **Recursive processing** - Automatically finds and processes nested ZIP files
- ✅ **Memory efficient** - Uses 7zip's streaming extraction
- ✅ **Cross-platform** - Works on Windows, Linux, macOS
- ✅ **Configurable depth** - Control how deep to recurse
- ✅ **List or extract modes** - Preview contents before extracting
- ✅ **Verbose output** - See detailed processing information

## Requirements

### Install 7zip:

**Windows:**
- Download from https://www.7-zip.org/
- Install and add to PATH, or place `7z.exe` in script directory

**Ubuntu/Debian:**
```bash
sudo apt-get install p7zip-full
```

**macOS:**
```bash
brew install p7zip
```

**CentOS/RHEL:**
```bash
sudo yum install p7zip
```

## Usage Examples

### Basic Usage
```bash
# List all files (including nested ZIPs)
./recursive-unzip.sh Manual.zip --list

# Extract everything to default directory (./extracted)
./recursive-unzip.sh Manual.zip

# Extract to specific directory
./recursive-unzip.sh Manual.zip -o ./my-manuals
```

### Advanced Options
```bash
# Limit recursion depth
./recursive-unzip.sh Manual.zip --list --max-depth 3

# Verbose output
./recursive-unzip.sh Manual.zip --verbose

# List only with verbose details
./recursive-unzip.sh Manual.zip --list --verbose --max-depth 5
```

### Windows Examples
```cmd
REM List contents
recursive-unzip.bat Manual.zip --list

REM Extract with options
recursive-unzip.bat Manual.zip -o manuals -d 5 --verbose
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-o, --output DIR` | Output directory | `./extracted` |
| `-d, --max-depth N` | Maximum recursion depth | `10` |
| `-l, --list` | List files only, don't extract | `false` |
| `-v, --verbose` | Verbose output | `false` |
| `-h, --help` | Show help | - |

## How It Works

1. **Initial Processing**: Script checks if the ZIP file is valid using `7z t`
2. **Extraction**: Uses `7z x` to extract files to temporary/output directories
3. **Nested Detection**: Scans extracted files for `.zip` extensions
4. **Recursive Processing**: For each nested ZIP found:
   - Creates a subdirectory named `{zipname}_extracted`
   - Recursively processes the nested ZIP
   - Continues until max depth is reached
5. **Cleanup**: Removes temporary files and directories

## Output Structure

When extracting, the script maintains the directory structure and creates additional directories for nested ZIPs:

```
extracted/
├── file1.txt
├── folder1/
│   ├── file2.txt
│   └── nested.zip
├── nested_extracted/          # Contents of nested.zip
│   ├── inner_file.txt
│   └── deeper.zip
└── deeper_extracted/          # Contents of deeper.zip
    └── deep_file.txt
```

## Performance Comparison

| Method | File Size Limit | Memory Usage | Speed | Nested ZIP Support |
|--------|----------------|--------------|-------|-------------------|
| **7zip Script** | No limit | Low (streaming) | Fast | ✅ Full |
| Node.js fflate | 5GB | High (in-memory) | Medium | ✅ Full |
| Browser React | 5GB | Browser limit | Medium | ✅ Full |

## Troubleshooting

### "7z command not found"
- Install 7zip using the instructions above
- On Windows, ensure 7zip is in your PATH or place `7z.exe` in the script directory

### "Permission denied"
- On Unix systems: `chmod +x recursive-unzip.sh`
- Ensure you have write permissions to the output directory

### Large file processing is slow
- This is normal for very large archives
- Use `--list` first to see what's inside before extracting
- Consider using `--max-depth` to limit recursion

### Script stops with errors
- Check if the ZIP file is corrupted: `7z t Manual.zip`
- Ensure sufficient disk space for extraction
- Try with `--verbose` to see detailed error messages

## Interactive Test Script

The `test-manual.sh` script provides an easy way to test your Manual.zip:

```bash
./test-manual.sh
```

This will:
1. Check if Manual.zip exists
2. Verify 7zip is installed
3. Test ZIP file integrity
4. Offer interactive options for listing or extracting

## Advantages of 7zip Approach

1. **No Memory Limits**: Unlike Node.js, 7zip can handle files of any size
2. **Proven Reliability**: 7zip is a mature, well-tested tool
3. **Cross-Platform**: Works consistently across different operating systems
4. **Efficient**: Uses streaming extraction, minimal memory footprint
5. **Flexible**: Easy to modify scripts for specific needs

## Next Steps

1. **Test with your Manual.zip**: Run `./test-manual.sh`
2. **List contents first**: Use `--list` to see what's inside
3. **Extract selectively**: You can modify the script to extract only certain file types
4. **Automate**: Integrate into your workflow or build processes

The 7zip approach is ideal for handling your large Manual.zip file and any nested archives it contains!
