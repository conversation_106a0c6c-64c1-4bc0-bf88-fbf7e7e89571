#!/bin/bash

# Recursive ZIP Processor using 7zip
# Handles nested ZIP files recursively

set -e  # Exit on any error

# Default configuration
MAX_DEPTH=10
OUTPUT_DIR="./extracted"
LIST_ONLY=false
VERBOSE=false
STATS_TOTAL_FILES=0
STATS_TOTAL_ZIPS=0

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print usage
print_usage() {
    echo "Recursive ZIP Processor using 7zip"
    echo ""
    echo "Usage: $0 <zip-file> [options]"
    echo ""
    echo "Options:"
    echo "  -o, --output DIR      Output directory (default: ./extracted)"
    echo "  -d, --max-depth N     Maximum recursion depth (default: 10)"
    echo "  -l, --list           List files only, don't extract"
    echo "  -v, --verbose        Verbose output"
    echo "  -h, --help           Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 Manual.zip                           # Extract with defaults"
    echo "  $0 Manual.zip -o ./manuals -d 5         # Extract to ./manuals, max depth 5"
    echo "  $0 Manual.zip --list --verbose          # List all files with details"
}

# Function to check if 7zip is installed
check_7zip() {
    if ! command -v 7z &> /dev/null; then
        print_color $RED "Error: 7zip (7z command) is not installed or not in PATH"
        echo ""
        echo "Install 7zip:"
        echo "  Ubuntu/Debian: sudo apt-get install p7zip-full"
        echo "  Windows Git Bash: Install 7-Zip and add to PATH"
        echo "  macOS: brew install p7zip"
        exit 1
    fi
}

# Function to format file size
format_size() {
    local bytes=$1
    if [ $bytes -lt 1024 ]; then
        echo "${bytes} B"
    elif [ $bytes -lt 1048576 ]; then
        echo "$(( bytes / 1024 )) KB"
    elif [ $bytes -lt 1073741824 ]; then
        echo "$(( bytes / 1048576 )) MB"
    else
        echo "$(( bytes / 1073741824 )) GB"
    fi
}

# Function to check if file is a ZIP archive
is_zip_file() {
    local file="$1"
    
    # Check by extension first
    if [[ "$file" =~ \.(zip|ZIP)$ ]]; then
        return 0
    fi
    
    # Check with 7z test command
    if 7z t "$file" &>/dev/null; then
        return 0
    fi
    
    return 1
}

# Function to list files in archive recursively
list_archive() {
    local archive="$1"
    local depth="$2"
    local prefix="$3"
    
    if [ $depth -gt $MAX_DEPTH ]; then
        print_color $YELLOW "Warning: Maximum depth ($MAX_DEPTH) reached for $archive"
        return
    fi
    
    local indent=""
    for ((i=0; i<depth; i++)); do
        indent="  $indent"
    done
    
    if [ $VERBOSE = true ]; then
        print_color $BLUE "${indent}Processing: $(basename "$archive") (depth: $depth)"
    fi
    
    # Create temp directory for this archive
    local temp_dir="./temp_extract_$$_$depth"
    mkdir -p "$temp_dir"
    
    # Extract archive to temp directory
    if ! 7z x "$archive" -o"$temp_dir" &>/dev/null; then
        print_color $RED "${indent}Error: Cannot extract $archive"
        rm -rf "$temp_dir"
        return
    fi
    
    # List all files and find nested ZIPs
    local nested_zips=()
    
    find "$temp_dir" -type f | while read -r file; do
        local rel_path="${file#$temp_dir/}"
        local full_path="$prefix$rel_path"
        local size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo 0)
        local size_str=$(format_size $size)
        
        if is_zip_file "$file"; then
            echo "${indent}🗜️ $full_path ($size_str) [ZIP]"
            nested_zips+=("$file")
            ((STATS_TOTAL_ZIPS++))
        else
            echo "${indent}📄 $full_path ($size_str)"
        fi
        
        ((STATS_TOTAL_FILES++))
    done
    
    # Process nested ZIP files
    find "$temp_dir" -name "*.zip" -o -name "*.ZIP" | while read -r nested_zip; do
        if [ -f "$nested_zip" ] && [ $depth -lt $MAX_DEPTH ]; then
            local rel_path="${nested_zip#$temp_dir/}"
            local new_prefix="$prefix$(dirname "$rel_path")/"
            if [ "$new_prefix" = "$prefix./" ]; then
                new_prefix="$prefix"
            fi
            list_archive "$nested_zip" $((depth + 1)) "$new_prefix"
        fi
    done
    
    # Clean up temp directory
    rm -rf "$temp_dir"
}

# Function to extract archive recursively
extract_archive() {
    local archive="$1"
    local output="$2"
    local depth="$3"
    
    if [ $depth -gt $MAX_DEPTH ]; then
        print_color $YELLOW "Warning: Maximum depth ($MAX_DEPTH) reached for $archive"
        return
    fi
    
    local indent=""
    for ((i=0; i<depth; i++)); do
        indent="  $indent"
    done
    
    if [ $VERBOSE = true ]; then
        print_color $BLUE "${indent}Extracting: $(basename "$archive") (depth: $depth)"
    fi
    
    # Create output directory
    mkdir -p "$output"
    
    # Extract the archive
    if ! 7z x "$archive" -o"$output" &>/dev/null; then
        print_color $RED "${indent}Error: Cannot extract $archive"
        return
    fi
    
    # Find and process nested ZIP files
    find "$output" -name "*.zip" -o -name "*.ZIP" | while read -r nested_zip; do
        if [ -f "$nested_zip" ] && [ $depth -lt $MAX_DEPTH ]; then
            local zip_name=$(basename "$nested_zip" .zip)
            local zip_dir="$(dirname "$nested_zip")/${zip_name}_extracted"
            
            if [ $VERBOSE = true ]; then
                print_color $CYAN "${indent}Found nested ZIP: $(basename "$nested_zip")"
            fi
            
            # Extract nested ZIP to its own directory
            extract_archive "$nested_zip" "$zip_dir" $((depth + 1))
            
            ((STATS_TOTAL_ZIPS++))
        fi
        ((STATS_TOTAL_FILES++))
    done
    
    # Count regular files
    find "$output" -type f ! -name "*.zip" ! -name "*.ZIP" | while read -r file; do
        ((STATS_TOTAL_FILES++))
        if [ $VERBOSE = true ]; then
            local size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo 0)
            print_color $GREEN "${indent}Extracted: $(basename "$file") ($(format_size $size))"
        fi
    done
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -d|--max-depth)
            MAX_DEPTH="$2"
            shift 2
            ;;
        -l|--list)
            LIST_ONLY=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            print_usage
            exit 0
            ;;
        *)
            ZIP_FILE="$1"
            shift
            ;;
    esac
done

# Use Manual.zip as default if no file specified
if [ -z "$ZIP_FILE" ]; then
    ZIP_FILE="Manual.zip"
fi

# Check if file exists
if [ ! -f "$ZIP_FILE" ]; then
    print_color $RED "Error: File '$ZIP_FILE' not found"
    exit 1
fi

# Check 7zip installation
check_7zip

# Print configuration
print_color $CYAN "=== Recursive ZIP Processor ==="
echo "File: $ZIP_FILE"
echo "Output directory: $OUTPUT_DIR"
echo "Maximum depth: $MAX_DEPTH"
echo "Mode: $([ "$LIST_ONLY" = true ] && echo "List only" || echo "Extract")"
echo "Verbose: $VERBOSE"

# Get file size
if command -v stat &> /dev/null; then
    FILE_SIZE=$(stat -c%s "$ZIP_FILE" 2>/dev/null || stat -f%z "$ZIP_FILE" 2>/dev/null || echo 0)
    print_color $BLUE "File size: $(format_size $FILE_SIZE)"
fi

# Check if it's a valid ZIP file
if ! is_zip_file "$ZIP_FILE"; then
    print_color $RED "Error: '$ZIP_FILE' does not appear to be a valid ZIP file"
    exit 1
fi

echo ""
print_color $GREEN "Starting processing..."

# Record start time
START_TIME=$(date +%s)

# Process the file
if [ "$LIST_ONLY" = true ]; then
    print_color $YELLOW "=== FILE LISTING ==="
    list_archive "$ZIP_FILE" 0 ""
else
    print_color $YELLOW "=== EXTRACTION ==="
    extract_archive "$ZIP_FILE" "$OUTPUT_DIR" 0
    print_color $GREEN "Files extracted to: $OUTPUT_DIR"
fi

# Calculate processing time
END_TIME=$(date +%s)
PROCESSING_TIME=$((END_TIME - START_TIME))

# Print final statistics
echo ""
print_color $CYAN "=== Processing Statistics ==="
echo "Total files processed: $STATS_TOTAL_FILES"
echo "Nested ZIP files found: $STATS_TOTAL_ZIPS"
echo "Processing completed in ${PROCESSING_TIME} seconds"

exit 0
