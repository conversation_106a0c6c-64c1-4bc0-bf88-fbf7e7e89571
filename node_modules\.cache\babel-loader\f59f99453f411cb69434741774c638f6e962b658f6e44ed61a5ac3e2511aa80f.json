{"ast": null, "code": "var ansiRegex = new RegExp([\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\"].join(\"|\"), \"g\");\n\n/**\n *\n * Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string.\n * Adapted from code originally released by Sindre Sorhus\n * Licensed the MIT License\n *\n * @param {string} string\n * @return {string}\n */\nfunction stripAnsi(string) {\n  if (typeof string !== \"string\") {\n    throw new TypeError(\"Expected a `string`, got `\".concat(typeof string, \"`\"));\n  }\n  return string.replace(ansiRegex, \"\");\n}\nexport default stripAnsi;", "map": {"version": 3, "names": ["ansiRegex", "RegExp", "join", "stripAnsi", "string", "TypeError", "concat", "replace"], "sources": ["C:/Users/<USER>/Documents/augment-projects/B<PERSON>er<PERSON>ip Decompressor and list/node_modules/webpack-dev-server/client/utils/stripAnsi.js"], "sourcesContent": ["var ansiRegex = new RegExp([\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\"].join(\"|\"), \"g\");\n\n/**\n *\n * Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string.\n * Adapted from code originally released by Sindre Sorhus\n * Licensed the MIT License\n *\n * @param {string} string\n * @return {string}\n */\nfunction stripAnsi(string) {\n  if (typeof string !== \"string\") {\n    throw new TypeError(\"Expected a `string`, got `\".concat(typeof string, \"`\"));\n  }\n  return string.replace(ansiRegex, \"\");\n}\nexport default stripAnsi;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,IAAIC,MAAM,CAAC,CAAC,8HAA8H,EAAE,0DAA0D,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;;AAEvO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAE;EACzB,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,SAAS,CAAC,4BAA4B,CAACC,MAAM,CAAC,OAAOF,MAAM,EAAE,GAAG,CAAC,CAAC;EAC9E;EACA,OAAOA,MAAM,CAACG,OAAO,CAACP,SAAS,EAAE,EAAE,CAAC;AACtC;AACA,eAAeG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}