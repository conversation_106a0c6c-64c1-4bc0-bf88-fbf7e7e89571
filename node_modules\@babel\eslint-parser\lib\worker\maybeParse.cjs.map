{"version": 3, "names": ["babel", "require", "convert", "astInfo", "extractParserOptionsPlugin", "getVisitorKeys", "getTokLabels", "ref", "extractParserOptionsConfigItem", "MULTIPLE_OVERRIDES", "module", "exports", "_asyncMaybeParse", "_asyncToGenerator", "code", "options", "createConfigItemAsync", "dirname", "__dirname", "type", "plugins", "concat", "ast", "parserOptions", "parseAsync", "err", "test", "message", "convertError", "convertFile", "asyncMaybeParse", "_x", "_x2", "apply", "arguments"], "sources": ["../../src/worker/maybeParse.cts"], "sourcesContent": ["import babel = require(\"./babel-core.cts\");\nimport convert = require(\"../convert/index.cts\");\nimport astInfo = require(\"./ast-info.cts\");\nimport extractParserOptionsPlugin = require(\"./extract-parser-options-plugin.cjs\");\n\nimport type { InputOptions, ConfigItem } from \"@babel/core\";\nimport type { AST, ParseResult } from \"../types.cts\";\n\nconst { getVisitorKeys, getTokLabels } = astInfo;\n\nconst ref = {};\nlet extractParserOptionsConfigItem: ConfigItem<any>;\n\nconst MULTIPLE_OVERRIDES = /More than one plugin attempted to override parsing/;\n\nexport = async function asyncMaybeParse(\n  code: string,\n  options: InputOptions,\n): Promise<{\n  ast: AST.Program | null;\n  parserOptions: ParseResult | null;\n}> {\n  if (!extractParserOptionsConfigItem) {\n    extractParserOptionsConfigItem = await babel.createConfigItemAsync(\n      [extractParserOptionsPlugin, ref],\n      { dirname: __dirname, type: \"plugin\" },\n    );\n  }\n  const { plugins } = options;\n  options.plugins = plugins.concat(extractParserOptionsConfigItem);\n\n  let ast;\n\n  try {\n    return {\n      parserOptions: await babel.parseAsync(code, options),\n      ast: null,\n    };\n  } catch (err) {\n    if (!MULTIPLE_OVERRIDES.test(err.message)) {\n      throw err;\n    }\n  }\n\n  // There was already a parserOverride, so remove our plugin.\n  options.plugins = plugins;\n\n  try {\n    ast = await babel.parseAsync(code, options);\n  } catch (err) {\n    throw convert.convertError(err);\n  }\n\n  return {\n    ast: convert.convertFile(ast, code, getTokLabels(), getVisitorKeys()),\n    parserOptions: null,\n  };\n};\n"], "mappings": ";;;;MAAOA,KAAK,GAAAC,OAAA,CAAW,kBAAkB;AAAA,MAClCC,OAAO,GAAAD,OAAA,CAAW,sBAAsB;AAAA,MACxCE,OAAO,GAAAF,OAAA,CAAW,gBAAgB;AAAA,MAClCG,0BAA0B,GAAAH,OAAA,CAAW,qCAAqC;AAKjF,MAAM;EAAEI,cAAc;EAAEC;AAAa,CAAC,GAAGH,OAAO;AAEhD,MAAMI,GAAG,GAAG,CAAC,CAAC;AACd,IAAIC,8BAA+C;AAEnD,MAAMC,kBAAkB,GAAG,oDAAoD;AAACC,MAAA,CAAAC,OAAA;EAAA,IAAAC,gBAAA,GAAAC,iBAAA,CAEvE,WACPC,IAAY,EACZC,OAAqB,EAIpB;IACD,IAAI,CAACP,8BAA8B,EAAE;MACnCA,8BAA8B,SAASR,KAAK,CAACgB,qBAAqB,CAChE,CAACZ,0BAA0B,EAAEG,GAAG,CAAC,EACjC;QAAEU,OAAO,EAAEC,SAAS;QAAEC,IAAI,EAAE;MAAS,CACvC,CAAC;IACH;IACA,MAAM;MAAEC;IAAQ,CAAC,GAAGL,OAAO;IAC3BA,OAAO,CAACK,OAAO,GAAGA,OAAO,CAACC,MAAM,CAACb,8BAA8B,CAAC;IAEhE,IAAIc,GAAG;IAEP,IAAI;MACF,OAAO;QACLC,aAAa,QAAQvB,KAAK,CAACwB,UAAU,CAACV,IAAI,EAAEC,OAAO,CAAC;QACpDO,GAAG,EAAE;MACP,CAAC;IACH,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ,IAAI,CAAChB,kBAAkB,CAACiB,IAAI,CAACD,GAAG,CAACE,OAAO,CAAC,EAAE;QACzC,MAAMF,GAAG;MACX;IACF;IAGAV,OAAO,CAACK,OAAO,GAAGA,OAAO;IAEzB,IAAI;MACFE,GAAG,SAAStB,KAAK,CAACwB,UAAU,CAACV,IAAI,EAAEC,OAAO,CAAC;IAC7C,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZ,MAAMvB,OAAO,CAAC0B,YAAY,CAACH,GAAG,CAAC;IACjC;IAEA,OAAO;MACLH,GAAG,EAAEpB,OAAO,CAAC2B,WAAW,CAACP,GAAG,EAAER,IAAI,EAAER,YAAY,CAAC,CAAC,EAAED,cAAc,CAAC,CAAC,CAAC;MACrEkB,aAAa,EAAE;IACjB,CAAC;EACH,CAAC;EAAA,SA1CuBO,eAAeA,CAAAC,EAAA,EAAAC,GAAA;IAAA,OAAApB,gBAAA,CAAAqB,KAAA,OAAAC,SAAA;EAAA;EAAA,OAAfJ,eAAe;AAAA", "ignoreList": []}