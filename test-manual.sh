#!/bin/bash

# Quick test script for Manual.zip
# This will list the contents first, then optionally extract

echo "=== Testing Manual.zip with 7zip ==="
echo ""

# Check if Manual.zip exists
if [ ! -f "Manual.zip" ]; then
    echo "❌ Manual.zip not found in current directory"
    echo "Please make sure Manual.zip is in the same directory as this script"
    exit 1
fi

# Check if 7zip is installed
if ! command -v 7z &> /dev/null; then
    echo "❌ 7zip not found. Installing instructions:"
    echo ""
    echo "Windows (Git Bash): Download and install 7-Zip from https://www.7-zip.org/"
    echo "Ubuntu/Debian: sudo apt-get install p7zip-full"
    echo "macOS: brew install p7zip"
    echo ""
    exit 1
fi

echo "✅ Found Manual.zip"
echo "✅ 7zip is available"
echo ""

# Get file size
FILE_SIZE=$(stat -c%s "Manual.zip" 2>/dev/null || stat -f%z "Manual.zip" 2>/dev/null || echo "unknown")
if [ "$FILE_SIZE" != "unknown" ]; then
    if [ $FILE_SIZE -lt 1048576 ]; then
        SIZE_STR="$(( FILE_SIZE / 1024 )) KB"
    elif [ $FILE_SIZE -lt 1073741824 ]; then
        SIZE_STR="$(( FILE_SIZE / 1048576 )) MB"
    else
        SIZE_STR="$(( FILE_SIZE / 1073741824 )) GB"
    fi
    echo "📦 File size: $SIZE_STR"
else
    echo "📦 File size: unknown"
fi

# Test if it's a valid ZIP
echo "🔍 Testing ZIP file integrity..."
if 7z t "Manual.zip" > /dev/null 2>&1; then
    echo "✅ Manual.zip is a valid ZIP archive"
else
    echo "❌ Manual.zip appears to be corrupted or not a valid ZIP file"
    exit 1
fi

echo ""
echo "Choose an option:"
echo "1) List contents only (recommended first)"
echo "2) Extract all files"
echo "3) Extract with verbose output"
echo "4) List with maximum depth of 3"
echo "5) Exit"
echo ""
read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo ""
        echo "📋 Listing contents of Manual.zip..."
        echo ""
        ./recursive-unzip.sh Manual.zip --list
        ;;
    2)
        echo ""
        echo "📂 Extracting Manual.zip to ./extracted..."
        echo ""
        ./recursive-unzip.sh Manual.zip -o ./extracted
        ;;
    3)
        echo ""
        echo "📂 Extracting Manual.zip with verbose output..."
        echo ""
        ./recursive-unzip.sh Manual.zip -o ./extracted --verbose
        ;;
    4)
        echo ""
        echo "📋 Listing contents with max depth 3..."
        echo ""
        ./recursive-unzip.sh Manual.zip --list --max-depth 3 --verbose
        ;;
    5)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "✅ Operation completed!"
echo ""
echo "💡 Tips:"
echo "- Use './recursive-unzip.sh Manual.zip --help' for all options"
echo "- For Windows, use 'recursive-unzip.bat Manual.zip' instead"
echo "- Large files may take some time to process"
