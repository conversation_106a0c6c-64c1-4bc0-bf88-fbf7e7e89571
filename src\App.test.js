import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import App from "./App";

test("renders BrowserZip Decompressor title", () => {
  render(<App />);
  const titleElement = screen.getByText(/BrowserZip Decompressor/i);
  expect(titleElement).toBeInTheDocument();
});

test("renders upload area", () => {
  render(<App />);
  const uploadText = screen.getByText(/Choose ZIP file or drag & drop/i);
  expect(uploadText).toBeInTheDocument();
});

test("shows 5GB limit in upload hint", () => {
  render(<App />);
  const limitText = screen.getByText(/Supports files up to 5GB/i);
  expect(limitText).toBeInTheDocument();
});

test("shows recursive ZIP exploration hint", () => {
  render(<App />);
  const recursiveText = screen.getByText(/recursive ZIP exploration/i);
  expect(recursiveText).toBeInTheDocument();
});
