# BrowserZip Decompressor

A React application that allows you to upload and explore ZIP files entirely in your browser without any server involvement. Perfect for handling large ZIP files up to 1GB.

## Features

- 🗜️ **Browser-only ZIP processing** - No server required, everything runs in your browser
- 📦 **Large file support** - Handles ZIP files up to 1GB
- 📋 **File listing** - View all files and directories in the ZIP archive
- 📊 **File information** - See file sizes, directory structure, and archive statistics
- ⬇️ **Individual file download** - Extract and download specific files from the archive
- 🎨 **Modern UI** - Clean, responsive design with progress indicators
- 🔒 **Privacy-focused** - Your files never leave your browser

## Technology Stack

- **React 18** - Modern React with hooks
- **fflate** - High-performance ZIP decompression library
- **Pure JavaScript** - No server-side dependencies

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd browserzip-decompressor
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Usage

1. **Upload a ZIP file**: Click on the upload area or drag and drop a ZIP file
2. **View file list**: Once processed, you'll see all files and directories in the archive
3. **Download files**: Click the download button next to any file to extract and download it
4. **Explore structure**: Navigate through the directory structure to understand the archive layout

## Supported File Types

- ZIP archives (.zip)
- Supports password-protected ZIP files (browser will prompt for password)
- Handles nested directories and various file types

## Performance Notes

- The application uses streaming decompression for better memory efficiency
- Large files (1GB+) may take some time to process depending on your device's capabilities
- Processing happens entirely in your browser's memory

## Browser Compatibility

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Build for Production

```bash
npm run build
```

This builds the app for production to the `build` folder.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.

## Acknowledgments

- [fflate](https://github.com/101arrowz/fflate) - For the excellent ZIP decompression library
- React team for the amazing framework
